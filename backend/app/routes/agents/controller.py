import asyncio
import json
from enum import Enum
from typing import Optional
from uuid import UUID

from agents.utils import format_agent_event_for_sse
from database.core import DbSession
from db_services import incident as incident_db_service
from db_services import runbooks as runbook_db_service
from fastapi import APIRouter, Body, HTTPException, Response, status
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from utils.logger import get_controller_logger

from routes.agents import service
from routes.auth.service import CurrentUser

logger = get_controller_logger("agents")
router = APIRouter(prefix="/agents", tags=["Agents - [Testing]"])


class QueryRequest(BaseModel):
    query: str
    session_id: Optional[str] = None


@router.post("/query_stream")
async def query_agent_stream(
    db: DbSession, current_user: CurrentUser, request: QueryRequest = Body(...)
):
    """
    Query the coordinator agent with streaming responses.
    Returns Server-Sent Events (SSE) stream of agent events.
    """
    query = request.query
    session_id = request.session_id
    logger.info(
        f"Processing general streaming query from user {current_user.get_uuid()} session {session_id}: {query[:100]}..."
    )

    async def event_generator():
        try:
            async for event in service.handle_general_query_streaming(
                db, current_user, session_id, query
            ):
                # Format event data for SSE using utility function
                event_data = format_agent_event_for_sse(event)

                # Send as SSE format
                yield f"data: {json.dumps(event_data)}\n\n"

                # Add small delay to prevent overwhelming the client
                await asyncio.sleep(0.01)

            # Send final event to indicate completion
            yield f"data: {json.dumps({'type': 'stream_complete', 'is_final': True})}\n\n"

        except Exception as e:
            logger.error(f"Error in streaming response: {str(e)}")
            error_data = {"type": "error", "error_message": str(e), "is_final": True}
            yield f"data: {json.dumps(error_data)}\n\n"

    logger.info(
        f"Starting enhanced streaming response for user {current_user.get_uuid()}"
    )
    return StreamingResponse(
        event_generator(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        },
    )


@router.post("/query")
async def query_agent(
    db: DbSession, current_user: CurrentUser, request: QueryRequest = Body(...)
):
    """
    Query the coordinator agent with a raw query.
    """
    query = request.query
    session_id = request.session_id
    logger.info(
        f"Processing general query from user {current_user.get_uuid()} session {session_id}: {query[:100]}..."
    )
    try:
        result = await service.handle_general_query(db, current_user, session_id, query)
        logger.info(
            f"Successfully processed general query for user {current_user.get_uuid()}"
        )
        return result
    except Exception as e:
        logger.error(f"Failed to process general query: {str(e)}")
        raise


@router.post("/{incident_id}/root_cause_agent", response_model=dict)
async def analyze_incident_root_cause(
    db: DbSession,
    current_user: CurrentUser,
    incident_id: UUID,
):
    """
    Analyze the root cause of an incident by directly invoking the root cause analyzer agent.
    """
    logger.info(
        f"Starting root cause analysis for incident {incident_id} by user {current_user.get_uuid()}"
    )
    incident = incident_db_service.get_incident_by_id(db, incident_id)
    if not incident:
        logger.warning(f"Incident not found: {incident_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Incident not found"
        )

    try:
        result = await service.handle_root_cause_agent_request(
            db, current_user, incident
        )
        logger.info(
            f"Successfully completed root cause analysis for incident {incident_id}"
        )
        return result
    except Exception as e:
        logger.error(
            f"Failed to analyze root cause for incident {incident_id}: {str(e)}"
        )
        raise


@router.post("/{incident_id}/runbook_agent")
async def generate_runbook_steps(
    db: DbSession,
    current_user: CurrentUser,
    incident_id: UUID,
    runbook_id: Optional[UUID] = None,
    runbook_type: Optional[str] = None,
):
    """
    Generate actionable solutions for an incident by directly invoking the runbook generator agent.
    """
    logger.info(
        f"Generating runbook steps for incident {incident_id} by user {current_user.get_uuid()}"
    )

    if not (runbook_id or runbook_type):
        logger.warning(f"Missing runbook_id or runbook_type for incident {incident_id}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Must specify either runbook_id or runbook_type",
        )

    try:
        incident = incident_db_service.get_incident_by_id(db, incident_id)
        if not incident:
            logger.warning(f"Incident not found: {incident_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Incident not found"
            )
        existing_steps = []
        if runbook_id:
            logger.info(
                f"Using existing runbook {runbook_id} for incident {incident_id}"
            )
            runbook = runbook_db_service.get_runbook_by_id(db, runbook_id)
            if not runbook:
                logger.warning(f"Runbook not found: {runbook_id}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Runbook not found"
                )

            runbook_type = runbook.type  # Override if runbook is provided
            runbook_steps = runbook_db_service.get_steps_by_runbook(db, runbook_id)
            existing_steps = [
                {
                    "title": step.title,
                    "description": step.description,
                    "details": step.details,
                    "expected_result": step.expected_result,
                    "status": (
                        step.status.value
                        if isinstance(step.status, Enum)
                        else step.status
                    ),
                    "notes": step.notes,
                }
                for step in runbook_steps
            ]
            logger.info(
                f"Found {len(existing_steps)} existing steps for runbook {runbook_id}"
            )

        if not runbook_type:
            logger.warning(f"Missing runbook_type for incident {incident_id}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Must specify runbook_type",
            )

        logger.info(
            f"Invoking runbook agent for incident {incident_id} with type {runbook_type}"
        )
        result = await service.handle_runbook_agent_request(
            db, current_user, incident, runbook_type, existing_steps
        )
        logger.info(f"Successfully generated runbook steps for incident {incident_id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to generate runbook steps for incident {incident_id}: {str(e)}"
        )
        raise


@router.post("/{incident_id}/reporter_agent")
async def generate_incident_report(
    db: DbSession,
    current_user: CurrentUser,
    incident_id: UUID,
):
    """
    Generate a detailed incident report by directly invoking the reporter agent.
    """
    if not incident_id:
        raise HTTPException(status_code=400, detail="Must specify incident_id")
    incident = incident_db_service.get_incident_by_id(db, incident_id)
    if not incident:
        raise HTTPException(status_code=404, detail="Incident not found")
    md_content = await service.handle_reporter_agent_request(
        db, current_user, incident_id
    )
    return Response(
        content=md_content,
        media_type="text/markdown",
        headers={
            "Content-Disposition": f'attachment; filename="incident_{incident_id}.md"'
        },
    )


@router.post("/log_analytics_agent")
async def log_analytics_agent(
    db: DbSession, current_user: CurrentUser, instruction: str
):
    return await service.handle_log_analytics_agent_request(
        db, current_user, instruction
    )


@router.get("/{incident_id}/incident_manager_agent")
async def incident_manager_agent(
    db: DbSession, current_user: CurrentUser, incident_id: UUID, query: str
):
    return await service.handle_incident_manager_agent_request(
        db, current_user, incident_id, query
    )
