import json
from typing import Any, Dict, List, Optional
from uuid import UUID

from agents.main_agent import coordinator_agent
from agents.sub_agents.incident_manager import utils as incident_manager_utils
from agents.sub_agents.incident_manager.agent import incident_manager_agent
from agents.sub_agents.log_analytics import utils as log_analytics_utils
from agents.sub_agents.log_analytics.agent import log_analytics_agent
from agents.sub_agents.reporter_agent import utils as reporter_utils
from agents.sub_agents.reporter_agent.agent import report_agent
from agents.sub_agents.root_cause_analyzer import utils as root_cause_analyzer_utils
from agents.sub_agents.root_cause_analyzer.agent import root_cause_analyzer
from agents.sub_agents.runbook_generator_agent import utils as runbook_generator_utils
from agents.sub_agents.runbook_generator_agent.agent import runbook_generator_agent
from agents.sub_agents.summary_agent import utils as summary_agent_utils
from agents.sub_agents.summary_agent.agent import summary_agent
from agents.utils import get_final_response, handle_agent_request
from database.core import DbSession
from entities.incident import Incident
from fastapi import HTTPException
from utils.logger import get_service_logger

from routes.auth.service import CurrentUser

logger = get_service_logger("agents")


async def handle_general_query(
    db: DbSession, current_user: CurrentUser, session_id: Optional[str], query: str
):
    logger.info(
        f"Handling general query from user {current_user.get_uuid()}: {query[:100]}..."
    )
    try:
        event_generator = await handle_agent_request(
            user_id=str(current_user.get_uuid()),
            session_id=session_id,
            query=query,
            agent=coordinator_agent,
        )
        response = await get_final_response(event_generator)
        logger.info(
            f"Successfully processed general query for user {current_user.get_uuid()}"
        )
        return response
    except Exception as e:
        logger.error(f"Failed to handle general query: {str(e)}")
        raise


async def handle_general_query_streaming(
    db: DbSession, current_user: CurrentUser, session_id: Optional[str], query: str
):
    """
    Handle general query with streaming responses.
    Yields events as they come from the Google ADK agent.
    """
    logger.info(
        f"Handling general streaming query from user {current_user.get_uuid()}: {query[:100]}..."
    )

    event_generator = await handle_agent_request(
        user_id=str(current_user.get_uuid()),
        session_id=session_id,
        query=query,
        agent=coordinator_agent,
    )
    async for event in event_generator:
        logger.debug(
            f"Received event from agent: {event.author}, Final: {event.is_final_response()}"
        )
        yield event


async def handle_root_cause_agent_request(
    db: DbSession, current_user: CurrentUser, incident: Incident
) -> Dict[str, Any]:
    logger.info(f"Handling root cause analysis request for incident {incident.id}")
    try:
        query = root_cause_analyzer_utils.build_query(incident)
        query = root_cause_analyzer_utils.pre_process(query)

        logger.debug(f"Invoking root cause analyzer for incident {incident.id}")
        event_generator = await handle_agent_request(
            user_id=str(current_user.get_uuid()),
            session_id=None,
            query=query,
            agent=root_cause_analyzer,
        )
        response = await get_final_response(event_generator)
        result = root_cause_analyzer_utils.post_process(response)

        try:
            parsed_result = json.loads(result)
            logger.info(
                f"Successfully completed root cause analysis for incident {incident.id}"
            )
            return parsed_result
        except Exception as e:
            logger.warning(
                f"Failed to parse root cause analysis result as JSON: {str(e)}"
            )
            return {"raw_response": result}
    except Exception as e:
        logger.error(
            f"Failed to handle root cause analysis for incident {incident.id}: {str(e)}"
        )
        raise


async def handle_runbook_agent_request(
    db: DbSession,
    current_user: CurrentUser,
    incident: Incident,
    runbook_type: str,
    runbook_steps: List[Dict[str, Any]],
) -> List[Dict[str, Any]]:
    logger.info(
        f"Handling runbook generation for incident {incident.id} with type {runbook_type}"
    )
    logger.debug(f"Existing runbook steps count: {len(runbook_steps)}")

    try:
        query = runbook_generator_utils.build_query(
            incident, runbook_type, runbook_steps
        )
        query = runbook_generator_utils.pre_process(query)

        logger.debug(f"Invoking runbook generator agent for incident {incident.id}")
        event_generator = await handle_agent_request(
            user_id=str(current_user.get_uuid()),
            session_id=None,
            query=query,
            agent=runbook_generator_agent,
        )
        response = await get_final_response(event_generator)
        result = runbook_generator_utils.post_process(response)

        try:
            parsed_result = json.loads(result)
            logger.info(
                f"Successfully generated {len(parsed_result)} runbook steps for incident {incident.id}"
            )
            return parsed_result["steps"]
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse runbook generation JSON result: {str(e)}")
            logger.debug(f"Raw result: {result}")
            return []
    except Exception as e:
        logger.error(
            f"Failed to handle runbook generation for incident {incident.id}: {str(e)}"
        )
        raise


async def handle_reporter_agent_request(
    db: DbSession,
    current_user: CurrentUser,
    incident_id: UUID,
) -> str:
    """
    Handle a request to generate an incident report using the reporter agent.
    """
    from fastapi import HTTPException

    try:
        query = reporter_utils.build_query(incident_id)
        query = reporter_utils.pre_process(query)
        event_generator = await handle_agent_request(
            user_id=str(current_user.get_uuid()),
            session_id=None,
            query=query,
            agent=report_agent,
        )
        response = await get_final_response(event_generator)
        result = reporter_utils.post_process(response)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to generate report: {str(e)}"
        )


async def handle_log_analytics_agent_request(
    db: DbSession, current_user: CurrentUser, user_query: str
) -> str:
    try:
        query = log_analytics_utils.build_query(user_query)
        query = log_analytics_utils.pre_process(query)

        event_generator = await handle_agent_request(
            user_id=str(current_user.get_uuid()),
            session_id=None,
            query=query,
            agent=log_analytics_agent,
        )
        response = await get_final_response(event_generator)
        result = log_analytics_utils.post_process(response)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to generate log data: {str(e)}"
        )


async def handle_summary_agent_request(
    db: DbSession, current_user: CurrentUser, incident: Incident
) -> str:
    logger.info(f"Handling summary generation request for incident {incident.id}")
    try:
        query = summary_agent_utils.build_query(incident)
        query = summary_agent_utils.pre_process(query)

        logger.debug(f"Invoking summary agent for incident {incident.id}")
        event_generator = await handle_agent_request(
            user_id=str(current_user.get_uuid()),
            session_id=None,
            query=query,
            agent=summary_agent,
        )

        response = await get_final_response(event_generator)
        result = summary_agent_utils.post_process(response)

        logger.info(
            f"Successfully completed summary generation for incident {incident.id}"
        )
        return result
    except Exception as e:
        logger.error(
            f"Failed to handle summary generation for incident {incident.id}: {str(e)}"
        )
        raise


async def handle_incident_manager_agent_request(
    db: DbSession, current_user: CurrentUser, incident_id: UUID, query: str
):
    query = incident_manager_utils.build_query(str(incident_id), query)
    query = incident_manager_utils.pre_process(query)
    result = await handle_agent_request(db, current_user, query, incident_manager_agent)
    return result
