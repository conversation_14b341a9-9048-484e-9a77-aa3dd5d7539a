from google.adk.agents import Agent
from google.adk.code_executors import BuiltInCodeExecutor
from google.adk.models.lite_llm import LiteLlm

AGENT_MODEL = "gemini/gemini-2.0-flash"  # only supports Gemini 2.0+ models

code_executor_agent = Agent(
    name="code_executor_agent",
    model=LiteLlm(AGENT_MODEL),
    description="Executes Python code",
    instruction="You're a specialist in Code Execution. You can execute Python code to perform calculations, data analysis, and other tasks.",
    code_executor=BuiltInCodeExecutor(),
)
