from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm
from google.adk.tools import load_memory

AGENT_MODEL = "gemini/gemini-2.0-flash"
memory_recall_agent = LlmAgent(
    model=LiteLlm(AGENT_MODEL),
    name="MemoryRecallAgent",
    instruction="Answer the user's question. Use the 'load_memory' tool, if the answer might be in past conversations.",
    tools=[load_memory],
)
