from google.adk.agents import Agent
from google.adk.models.lite_llm import Lite<PERSON><PERSON>
from google.adk.planners import BuiltInPlanner
from google.adk.tools.agent_tool import AgentTool
from google.genai import types

from agents.enhanced_workflows.enhanced_coordinator import workflow_coordinator_agent
from agents.guardrail import safety_guardrail
from agents.inbuilt_agents import google_search_agent, memory_recall_agent
from agents.sub_agents import (
    incident_manager_agent,
    kb_manager_agent,
    log_analytics_agent,
    preference_agent,
    report_agent,
    root_cause_analyzer,
    runbook_generator_agent,
    time_agent,
)

from . import prompt

# AGENT_MODEL = "ollama/qwen3:4b"
# AGENT_MODEL = "gemini/gemini-2.0-flash"
AGENT_MODEL = "openai/gpt-4o-mini"
# AGENT_MODEL = "gemini/gemini-2.5-flash-preview-05-20"

coordinator_agent = Agent(
    name="Abilytics_AI_Assistant",
    model=LiteLlm(AGENT_MODEL),
    description="Abilytics Incident Assistant that helps resolve incidents through systematic investigation and iterative problem-solving.",
    instruction=prompt.INSTRUCTION,
    planner=BuiltInPlanner(
        thinking_config=types.ThinkingConfig(
            include_thoughts=True, thinking_budget=1024
        )
    ),
    sub_agents=[time_agent, preference_agent],
    tools=[
        AgentTool(agent=workflow_coordinator_agent, skip_summarization=False),
        AgentTool(agent=incident_manager_agent, skip_summarization=False),
        AgentTool(agent=kb_manager_agent, skip_summarization=False),
        AgentTool(agent=log_analytics_agent, skip_summarization=False),
        AgentTool(agent=root_cause_analyzer, skip_summarization=False),
        AgentTool(agent=runbook_generator_agent, skip_summarization=False),
        AgentTool(agent=report_agent, skip_summarization=False),
        AgentTool(agent=memory_recall_agent, skip_summarization=False),
        AgentTool(agent=google_search_agent, skip_summarization=False),
    ],
    before_model_callback=safety_guardrail,
)
