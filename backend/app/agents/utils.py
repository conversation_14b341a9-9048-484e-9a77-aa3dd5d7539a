import uuid
from collections.abc import Async<PERSON>enerator
from datetime import datetime, timezone
from typing import Any, Optional

from fastapi import HTTPEx<PERSON>
from google.adk.agents import LlmAgent
from utils.logger import get_service_logger

from agents.agent_runner import (
    call_agent_async,
    create_session,
    get_runner,
    get_session,
)

# Setup centralized logging
logger = get_service_logger("agents_utils")


async def get_final_response(event_generator: AsyncGenerator) -> str:
    final_response_text = "Agent did not produce a final response."  # Default
    async for event in event_generator:
        if event.is_final_response():
            if event.content and event.content.parts:
                final_response_text = event.content.parts[0].text
            elif event.actions and event.actions.escalate:
                final_response_text = (
                    f"Agent escalated: {event.error_message or 'No specific message.'}"
                )
            break  # Stop processing events once the final response is found
    return final_response_text


def generate_session_id(user_id: str) -> str:
    """
    Generate a unique session ID using a UUID based on the current datetime.
    """

    dt_str = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S%f")
    return f"session_{dt_str}_{user_id}_{uuid.uuid4().hex}"


APP_NAME = "test_app"
INITIAL_STATE = {"user:preferences": {"language": "English"}}


async def handle_agent_request(
    user_id: str, session_id: Optional[str], query: str, agent: LlmAgent
) -> AsyncGenerator:
    if not user_id:
        logger.warning("Unauthorized agent request attempt")
        raise HTTPException(status_code=401, detail="Unauthorized")

    logger.info(f"Processing agent request for user {user_id}")

    session_id = session_id or generate_session_id(user_id)
    if not await get_session(APP_NAME, user_id, session_id):
        await create_session(APP_NAME, user_id, session_id, INITIAL_STATE)

    runner = get_runner(APP_NAME, agent)
    logger.info(f"Agent request processed for user {user_id}")
    return call_agent_async(query, runner, user_id, session_id)


def format_agent_event_for_sse(event) -> dict:
    """
    Format a Google ADK agent event for Server-Sent Events (SSE) streaming.

    Handles all major event types as defined in the ADK documentation:
    - User input events
    - Agent text responses (streaming and final)
    - Tool call requests
    - Tool result responses
    - State/artifact updates
    - Control flow signals (transfer, escalate)
    - Error events

    Args:
        event: Google ADK event object

    Returns:
        dict: Formatted event data suitable for JSON serialization
    """
    # Base event data structure
    event_data: dict[str, Any] = {
        "author": getattr(event, "author", "unknown"),
        "type": type(event).__name__,
        "event_id": getattr(event, "id", None),
        "invocation_id": getattr(event, "invocation_id", None),
        "is_final": (
            event.is_final_response() if hasattr(event, "is_final_response") else False
        ),
        "is_partial": getattr(event, "partial", False),
        "turn_complete": getattr(event, "turn_complete", None),
        "content": None,
        "content_type": None,
        "error_code": getattr(event, "error_code", None),
        "error_message": getattr(event, "error_message", None),
        "timestamp": None,
        "actions": {},
        "function_calls": [],
        "function_responses": [],
    }

    # Handle timestamp
    if hasattr(event, "timestamp"):
        event_data["timestamp"] = str(event.timestamp)

    # Handle function calls (tool requests)
    if hasattr(event, "get_function_calls") and event.get_function_calls():
        calls = event.get_function_calls()
        event_data["function_calls"] = [
            {"name": call.name, "args": call.args if hasattr(call, "args") else {}}
            for call in calls
        ]
        event_data["content_type"] = "tool_call_request"
        event_data["content"] = (
            f"Tool Call Requested: {', '.join(call.name for call in calls)}"
        )

    # Handle function responses (tool results)
    elif hasattr(event, "get_function_responses") and event.get_function_responses():
        responses = event.get_function_responses()
        event_data["function_responses"] = [
            {
                "name": response.name,
                "response": (
                    str(response.response) if hasattr(response, "response") else {}
                ),
            }
            for response in responses
        ]
        event_data["content_type"] = "tool_result"
        event_data["content"] = (
            f"Tool Result Received: {', '.join(response.name for response in responses)}"
        )

    # Handle text content
    elif (
        hasattr(event, "content")
        and event.content
        and hasattr(event.content, "parts")
        and event.content.parts
    ):
        if hasattr(event.content.parts[0], "text") and event.content.parts[0].text:
            event_data["content"] = event.content.parts[0].text
            if event_data["is_partial"]:
                event_data["content_type"] = "streaming_text"
            else:
                event_data["content_type"] = "complete_text"

    # Handle actions and control flow signals
    if hasattr(event, "actions") and event.actions:
        actions_data = {}

        # State changes
        if hasattr(event.actions, "state_delta") and event.actions.state_delta:
            actions_data["state_delta"] = event.actions.state_delta

        # Artifact changes
        if hasattr(event.actions, "artifact_delta") and event.actions.artifact_delta:
            actions_data["artifact_delta"] = event.actions.artifact_delta

        # Control flow signals
        if (
            hasattr(event.actions, "transfer_to_agent")
            and event.actions.transfer_to_agent
        ):
            actions_data["transfer_to_agent"] = event.actions.transfer_to_agent
            if not event_data["content"]:
                event_data["content"] = (
                    f"Transfer to agent: {event.actions.transfer_to_agent}"
                )
                event_data["content_type"] = "agent_transfer"

        if hasattr(event.actions, "escalate") and event.actions.escalate:
            actions_data["escalate"] = True
            if not event_data["content"]:
                event_data["content"] = (
                    f"Agent escalated: {event.error_message or 'No specific message.'}"
                )
                event_data["content_type"] = "escalation"

        if (
            hasattr(event.actions, "skip_summarization")
            and event.actions.skip_summarization
        ):
            actions_data["skip_summarization"] = True

        event_data["actions"] = actions_data

        # Handle state/artifact only events
        if not event_data["content"] and (
            actions_data.get("state_delta") or actions_data.get("artifact_delta")
        ):
            event_data["content_type"] = "state_update"
            changes = []
            if actions_data.get("state_delta"):
                changes.append(f"State: {list(actions_data['state_delta'].keys())}")
            if actions_data.get("artifact_delta"):
                changes.append(
                    f"Artifacts: {list(actions_data['artifact_delta'].keys())}"
                )
            event_data["content"] = f"Updated - {', '.join(changes)}"

    # Handle error events
    if event_data["error_code"] or event_data["error_message"]:
        event_data["content_type"] = "error"
        if not event_data["content"]:
            event_data["content"] = (
                f"Error: {event_data['error_message'] or 'Unknown error occurred'}"
            )

    # Handle long-running tools
    if hasattr(event, "long_running_tool_ids") and event.long_running_tool_ids:
        event_data["content_type"] = "long_running_tool"
        if not event_data["content"]:
            event_data["content"] = "Tool is running in background..."

    # Default content type if not set
    if not event_data["content_type"]:
        if event_data["author"] == "user":
            event_data["content_type"] = "user_input"
        else:
            event_data["content_type"] = "unknown"

    return event_data
