import re
from datetime import datetime
from typing import Any, Dict, Optional

from google.adk.agents.callback_context import CallbackContext
from google.adk.models.llm_request import LlmRequest
from google.adk.models.llm_response import LlmResponse
from google.genai import types


class SafetyGuardrail:
    """
    Safety-focused guardrail system that enforces AI safety boundaries and prevents harmful actions.

    Responsibilities:
    - Prevent execution of dangerous system commands
    - Block harmful, offensive, or inappropriate content
    - Ensure responsible AI behavior
    - Enforce security boundaries
    """

    def __init__(self):
        # Critical system commands that pose security risks
        self.dangerous_commands = [
            "rm -rf",
            "DROP DATABASE",
            "DELETE FROM users",
            "TRUNCATE",
            "shutdown",
            "reboot",
            "kill -9",
            "format",
            "fdisk",
            "dd if=",
            "chmod 777",
            "chown root",
        ]

        # Patterns that indicate attempts to bypass security or cause harm
        self.security_violation_patterns = [
            r"bypass.*security",
            r"disable.*firewall",
            r"grant.*admin",
            r"sudo.*rm",
            r"exec.*shell",
            r"eval.*input",
        ]

        # Content that violates responsible AI principles
        self.harmful_content_patterns = [
            r"hack.*system",
            r"exploit.*vulnerability",
            r"social.*engineer",
            r"phishing.*attack",
        ]

    def assess_safety_risk(self, text: str) -> Dict[str, Any]:
        """
        Assess if the input poses safety or security risks.
        Returns risk assessment without making business logic decisions.
        """
        risk_result = {
            "is_safe": True,
            "risk_level": "low",  # low, medium, high, critical
            "violations": [],
            "requires_block": False,
        }

        # Check for dangerous system commands
        for command in self.dangerous_commands:
            if command.lower() in text.lower():
                risk_result["violations"].append(
                    {
                        "type": "dangerous_command",
                        "detected": command,
                        "severity": "critical",
                    }
                )
                risk_result["risk_level"] = "critical"
                risk_result["is_safe"] = False
                risk_result["requires_block"] = True

        # Check for security violations
        for pattern in self.security_violation_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                risk_result["violations"].append(
                    {
                        "type": "security_violation",
                        "pattern": pattern,
                        "severity": "high",
                    }
                )
                if risk_result["risk_level"] != "critical":
                    risk_result["risk_level"] = "high"
                risk_result["is_safe"] = False

        # Check for harmful content
        for pattern in self.harmful_content_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                risk_result["violations"].append(
                    {"type": "harmful_content", "pattern": pattern, "severity": "high"}
                )
                if risk_result["risk_level"] not in ["critical", "high"]:
                    risk_result["risk_level"] = "high"
                risk_result["is_safe"] = False

        return risk_result


def safety_guardrail(
    callback_context: CallbackContext, llm_request: LlmRequest
) -> Optional[LlmResponse]:
    """
    Safety-focused guardrail system that enforces AI safety boundaries.

    Core responsibilities:
    - Block dangerous operations that could harm systems
    - Prevent harmful or inappropriate content generation
    - Ensure responsible AI behavior
    - Log safety violations for monitoring
    """
    agent_name = callback_context.agent_name
    guardrail = SafetyGuardrail()

    print(f"--- Safety Guardrail: Monitoring agent: {agent_name} ---")

    # Extract the text from the latest user message
    user_input = ""
    if llm_request.contents:
        for content in reversed(llm_request.contents):
            if content.role == "user" and content.parts:
                if content.parts[0].text:
                    user_input = content.parts[0].text
                    break

    if not user_input:
        return None

    print(f"--- Safety Guardrail: Analyzing input: '{user_input[:100]}...' ---")

    # Assess safety risks
    safety_assessment = guardrail.assess_safety_risk(user_input)

    # Store safety assessment for other components to use if needed
    callback_context.state["safety_assessment"] = {
        "assessment": safety_assessment,
        "timestamp": datetime.now().isoformat(),
        "agent": agent_name,
    }

    # Block if critical safety violations detected
    if safety_assessment["requires_block"]:
        print("--- Safety Guardrail: BLOCKING unsafe operation ---")
        callback_context.state["safety_blocked"] = True

        violations_text = "\n".join(
            [
                f"• {v['type'].replace('_', ' ').title()}: {v.get('detected', v.get('pattern', 'Unknown'))}"
                for v in safety_assessment["violations"]
            ]
        )

        return LlmResponse(
            content=types.Content(
                role="model",
                parts=[
                    types.Part(
                        text=f"""🚨 **OPERATION BLOCKED FOR SAFETY**

I cannot process this request as it contains potentially harmful operations that could compromise system security or safety.

**Safety Violations Detected:**
{violations_text}

For your safety and security, I cannot assist with operations that could:
- Damage or compromise systems
- Bypass security measures
- Execute dangerous commands
- Violate responsible AI principles

Please rephrase your request to focus on safe and constructive operations."""
                    )
                ],
            )
        )

    # Log high-risk operations for monitoring (without blocking)
    if safety_assessment["risk_level"] in ["medium", "high"]:
        print(
            f"--- Safety Guardrail: Logging {safety_assessment['risk_level']} risk operation ---"
        )
        callback_context.state["safety_risk_logged"] = {
            "level": safety_assessment["risk_level"],
            "violations": safety_assessment["violations"],
        }

    # Allow processing to continue for safe operations
    return None
