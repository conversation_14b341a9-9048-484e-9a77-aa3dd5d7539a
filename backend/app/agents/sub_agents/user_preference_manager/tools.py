from google.adk.tools.tool_context import <PERSON>lContext


def update_user_preference(
    category: str, preference: str, tool_context: ToolContext
) -> dict:
    """Updates a user's preference for a specific category with validation and persistence.

    This function provides secure, reliable preference management for Site Reliability Engineering
    operations, ensuring user preferences are properly validated, stored, and maintained across
    sessions. It supports comprehensive preference categories that enhance operational efficiency.

    Args:
        category (str): The preference category to update. Supported categories include:
                       - "notifications": Alert settings, escalation rules, communication channels
                       - "display": Dashboard layouts, data visualization options, theme settings
                       - "workflow": Default actions, automation settings, tool configurations
                       - "security": Authentication settings, access controls, audit preferences
                       - "integrations": External tool connections, API configurations
        preference (str): The preference value to set. Must be appropriate for the category type.
                         Examples: "dark", "email", "auto", "enabled", "slack_webhook_url"
        tool_context (ToolContext): ADK tool context for state management (automatically provided)

    Returns:
        dict: A structured response containing:
            - "status" (str): Operation status - "success" or "error"
            - "message" (str): If successful, confirmation of preference update with details
            - "error_message" (str): If error, detailed explanation of what went wrong
            - "previous_value" (str, optional): Previous preference value if it existed
            - "category" (str): Confirmed category that was updated
            - "new_value" (str): Confirmed new preference value

    Raises:
        ValueError: If category or preference values are invalid or unsupported
        StateError: If preference state cannot be accessed or updated
        Exception: For other unexpected errors during preference management

    Example:
        >>> result = update_user_preference("notifications", "email", tool_context)
        >>> print(result)
        {
            "status": "success",
            "message": "Your notifications preference has been set to email",
            "previous_value": "slack",
            "category": "notifications",
            "new_value": "email"
        }

    Usage Guidelines:
        - Always confirm preference changes with users before calling
        - Validate preference values are appropriate for the category
        - Consider security implications of preference modifications
        - Maintain audit trail of preference changes for compliance
        - Ensure preferences enhance rather than complicate user workflows
        - Use consistent preference naming conventions across categories
    """
    try:
        # Validate category
        valid_categories = ["notifications", "display", "workflow", "security", "integrations"]
        if category not in valid_categories:
            return {
                "status": "error",
                "error_message": f"Invalid category '{category}'. Supported categories: {', '.join(valid_categories)}"
            }

        # Access current preferences or initialize if none exist
        user_prefs_key = (
            "user:preferences"  # Using user: prefix makes this persistent across sessions
        )
        """
        No prefix: Session-specific, persists only for the current session
        user:: User-specific, persists across all sessions for a particular user
        app:: Application-wide, shared across all users and sessions
        temp:: Temporary, exists only during the current execution cycle
        """
        preferences = tool_context.state.get(user_prefs_key, {})

        # Store previous value for response
        previous_value = preferences.get(category)

        # Update the preferences
        preferences[category] = preference

        # Save back to state
        tool_context.state[user_prefs_key] = preferences

        print(f"Tool: Updated user preference '{category}' to '{preference}'")

        response = {
            "status": "success",
            "message": f"Your {category} preference has been set to {preference}",
            "category": category,
            "new_value": preference
        }

        if previous_value:
            response["previous_value"] = previous_value

        return response

    except Exception as e:
        return {
            "status": "error",
            "error_message": f"Failed to update preference: {str(e)}"
        }
