import os
from contextlib import contextmanager
from typing import Any, Dict, List, Optional
from uuid import UUID

from database.core import get_db
from db_services import events as events_db_service
from db_services import incident as incident_db_service
from db_services import runbooks as runbooks_db_service
from entities.incident_metrics import IncidentMetric


@contextmanager
def get_db_session():
    db = next(get_db())
    try:
        yield db
    finally:
        db.close()


def get_incident_details(incident_id: Optional[str] = None) -> dict:
    """Retrieves comprehensive incident details including basic metadata and extended information for report generation.

    This function provides complete incident information required for comprehensive incident reporting,
    including all metadata, detailed descriptions, affected services, and related information necessary
    for creating authoritative incident documentation.

    Args:
        incident_id (str, optional): UUID string of the incident to retrieve details for.
                                   Must be a valid UUID format corresponding to an existing incident.

    Returns:
        dict: A comprehensive incident details dictionary containing:
            - incident_number (str): Human-readable incident reference number
            - title (str): Brief incident title
            - summary (str): Detailed incident summary
            - priority (str): Incident priority level (P0, P1, P2, P3, P4)
            - severity (str): Impact severity assessment
            - incident_type (str): Classification of incident type
            - status (str): Current incident status
            - reported_at (str): ISO timestamp when incident was reported
            - reporter (str): Name/identifier of incident reporter
            - affected_services (List[str]): List of impacted services/systems
            - incident_details (str): Extended incident description and context
            - tags (List[str]): Associated incident tags and categories

    Raises:
        ValueError: If incident_id is not provided, has invalid UUID format, or incident not found
        DatabaseError: For database connection or query execution errors
        Exception: For other unexpected errors during incident retrieval

    Example:
        >>> details = get_incident_details("550e8400-e29b-41d4-a716-************")
        >>> print(f"Incident: {details['incident_number']} - {details['title']}")
        >>> print(f"Priority: {details['priority']}, Status: {details['status']}")

    Usage Guidelines:
        - Required as first step in incident report generation process
        - Provides foundational data for all report sections
        - Validates incident existence before proceeding with other data collection
        - Essential for report header and overview sections
    """
    if not incident_id:
        raise ValueError("incident_id parameter is required")

    try:
        print(f"Getting incident details from {os.getenv('DATABASE_URL')}")
        with get_db_session() as db:
            try:
                incident_uuid = UUID(incident_id)
            except ValueError as e:
                raise ValueError(f"Invalid incident_id format: {incident_id}") from e

            incident = incident_db_service.get_incident_by_id(db, incident_uuid)
            if not incident:
                raise ValueError(f"Incident not found with id: {incident_id}")

            incident_details = incident_db_service.get_incident_details(
                db, incident_uuid
            )

            result: dict = {
                "incident_number": incident.incident_number,
                "title": incident.title,
                "summary": incident.summary,
                "priority": incident.priority.value
                if incident.priority
                else "Not specified",
                "severity": incident.severity.value
                if incident.severity
                else "Not specified",
                "incident_type": incident.incident_type.value
                if incident.incident_type
                else "Not specified",
                "status": incident.status.value if incident.status else "Not specified",
                "reported_at": incident.reported_at.isoformat()
                if incident.reported_at
                else "Not specified",
                "reporter": incident.reported_by or "Not specified",
                "affected_services": incident_details.affected_services
                if incident_details
                else [],
                "tags": incident_details.tags if incident_details else [],
                "incident_details": incident_details.incident_details
                if incident_details
                else "No additional details available",
                "attachments": incident_details.attachments if incident_details else [],
                "closed_at": incident.closed_at.isoformat()
                if hasattr(incident, "closed_at") and incident.closed_at
                else None,
                "resolution_summary": incident_details.resolution_summary
                if incident_details and hasattr(incident_details, "resolution_summary")
                else None,
            }
            return result
    except ValueError:
        raise
    except Exception as e:
        print(f"Unexpected error retrieving incident details: {str(e)}")
        raise Exception(f"Failed to retrieve incident details: {str(e)}") from e


def get_incident_timeline(incident_id: str) -> List[Dict[str, Any]]:
    """Get incident timeline events"""
    if not incident_id:
        raise ValueError("incident_id is required")

    try:
        with get_db_session() as db:
            incident_uuid = UUID(incident_id)
            events, _ = events_db_service.get_incident_events(
                db, incident_uuid, limit=100
            )

            timeline = []
            for event in events:
                timeline.append(
                    {
                        "event_datetime": event.event_datetime.isoformat()
                        if event.event_datetime
                        else None,
                        "event_type": event.event_type.value
                        if event.event_type
                        else None,
                        "event_name": event.event_name,
                        "entity": event.user.name if event.user else "System",
                        "event_details": event.event_details,
                    }
                )

            return timeline
    except Exception as e:
        print(f"Error retrieving incident timeline: {str(e)}")
        return []


def get_incident_runbooks(incident_id: str) -> List[Dict[str, Any]]:
    """Get runbooks associated with the incident"""
    if not incident_id:
        raise ValueError("incident_id is required")

    try:
        with get_db_session() as db:
            incident_uuid = UUID(incident_id)
            runbooks = runbooks_db_service.list_runbooks(db, incident_uuid)

            runbooks_data = []
            for runbook in runbooks:
                steps = runbooks_db_service.get_steps_by_runbook(db, runbook.id)
                steps_data = []

                for step in steps:
                    steps_data.append(
                        {
                            "step_order": step.step_order,
                            "title": step.title,
                            "status": step.status.value if step.status else None,
                            "executed_at": step.executed_at.isoformat()
                            if step.executed_at
                            else None,
                            "notes": step.notes,
                        }
                    )

                runbooks_data.append(
                    {
                        "title": runbook.title,
                        "type": runbook.runbook_type.value
                        if runbook.runbook_type
                        else None,
                        "purpose": runbook.purpose,
                        "steps": steps_data,
                    }
                )

            return runbooks_data
    except Exception as e:
        print(f"Error retrieving incident runbooks: {str(e)}")
        return []


def get_incident_metrics(incident_id: str) -> Dict[str, Any]:
    """Get incident metrics and timing information"""
    if not incident_id:
        raise ValueError("incident_id is required")

    try:
        with get_db_session() as db:
            incident_uuid = UUID(incident_id)
            metrics = (
                db.query(IncidentMetric)
                .filter(IncidentMetric.incident_id == incident_uuid)
                .first()
            )

            if not metrics:
                return {}

            # Calculate durations in human-readable format
            def format_duration(start, end):
                if not start or not end:
                    return "Not available"
                delta = end - start
                hours, remainder = divmod(delta.total_seconds(), 3600)
                minutes, seconds = divmod(remainder, 60)
                return f"{int(hours)}h {int(minutes)}m {int(seconds)}s"

            return {
                "time_to_detection": format_duration(
                    metrics.detected_time, metrics.reported_time
                )
                if metrics.detected_time
                else "Not available",
                "time_to_acknowledgment": format_duration(
                    metrics.reported_time, metrics.acknowledged_time
                )
                if metrics.acknowledged_time
                else "Not available",
                "time_to_resolution": format_duration(
                    metrics.reported_time, metrics.resolved_time
                )
                if metrics.resolved_time
                else "Not available",
                "total_downtime": format_duration(
                    metrics.detected_time or metrics.reported_time,
                    metrics.resolved_time,
                )
                if metrics.resolved_time
                else "Ongoing",
                "detected_time": metrics.detected_time.isoformat()
                if metrics.detected_time
                else None,
                "reported_time": metrics.reported_time.isoformat()
                if metrics.reported_time
                else None,
                "acknowledged_time": metrics.acknowledged_time.isoformat()
                if metrics.acknowledged_time
                else None,
                "resolved_time": metrics.resolved_time.isoformat()
                if metrics.resolved_time
                else None,
                "closed_time": metrics.closed_time.isoformat()
                if metrics.closed_time
                else None,
            }
    except Exception as e:
        print(f"Error retrieving incident metrics: {str(e)}")
        return {}


def get_incident_ai_analysis(incident_id: str) -> Dict[str, Any]:
    """Get AI-generated analysis for the incident"""
    if not incident_id:
        raise ValueError("incident_id is required")

    try:
        with get_db_session() as db:
            incident_uuid = UUID(incident_id)
            analysis = incident_db_service.get_incident_ai_analysis(db, incident_uuid)

            if not analysis:
                return {}

            return {
                "root_cause": analysis.get("root_cause"),
                "immediate_action": analysis.get("immediate_action"),
                "impact_forecast": analysis.get("impact_forecast"),
                "cascading_risks": analysis.get("cascading_risks"),
            }
    except Exception as e:
        print(f"Error retrieving incident AI analysis: {str(e)}")
        return {}


def validate_incident_exists(incident_id: str) -> bool:
    """Validates that an incident exists and is accessible for report generation.

    This function serves as the mandatory first step in the incident reporting process,
    ensuring that the specified incident exists in the database and is accessible for
    comprehensive data collection and report generation.

    Args:
        incident_id (str): UUID string of the incident to validate.
                          Must be a valid UUID format corresponding to an existing incident.

    Returns:
        bool: True if incident exists and is accessible, False otherwise.

    Raises:
        ValueError: If incident_id has invalid UUID format
        DatabaseError: For database connection or query execution errors
        Exception: For other unexpected errors during validation

    Example:
        >>> if validate_incident_exists("550e8400-e29b-41d4-a716-************"):
        ...     print("Incident exists, proceeding with report generation")
        ... else:
        ...     print("Incident not found, cannot generate report")

    Usage Guidelines:
        - MUST be called first before any other report generation tools
        - Prevents wasted effort on non-existent incidents
        - Provides early validation for report generation workflow
        - Essential for ensuring report generation process integrity
    """
    try:
        with get_db_session() as db:
            incident_uuid = UUID(incident_id)
            incident = incident_db_service.get_incident_by_id(db, incident_uuid)
            return incident is not None
    except Exception:
        return False
