from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm

from ..common_tools import get_current_datetime
from . import prompt, tools

AGENT_MODEL = "gemini/gemini-2.0-flash"


log_analytics_agent = Agent(
    name="log_analytics_agent",
    model=LiteLlm(AGENT_MODEL),
    description="The log analytics agent can retrieve and analyze logs from all or any services for a given time range, generate human readable log summaries and insights from the logs.",
    instruction=prompt.INSTRUCTION,
    tools=[
        get_current_datetime,
        tools.fetch_logs,
        tools.generate_log_summary,
        tools.generate_log_insights,
    ],
)
