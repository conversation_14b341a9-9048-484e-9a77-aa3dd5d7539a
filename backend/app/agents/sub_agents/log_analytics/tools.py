import json
import re

from google.adk.tools.tool_context import ToolContext
from litellm import completion
from routes.logs import service
from routes.logs.models import LogQueryParams, LogsResponse


async def fetch_logs(params: dict, tool_context: ToolContext) -> LogsResponse:
    """Fetches logs from the system using Loki query language for incident investigation and analysis.

    This function provides access to system logs within specified time ranges using Loki's powerful
    query language. It's designed for incident investigation, allowing precise filtering and retrieval
    of relevant log entries to understand system behavior during incident timeframes.

    Args:
        params (dict): A dictionary containing log query parameters:
            query (str): The Loki query string to filter logs. Must be valid Loki syntax.
                        Examples:
                        - {job=~".+"} - Fetch all logs from all services
                        - '{job="api-service"}' - All logs from api-service
                        - '{job="database"} |= "ERROR"' - Error logs from database
                        - '{job="web-server"} |~ "5[0-9][0-9]"' - HTTP 5xx errors
                        - '{job=~"api.*"} |= "timeout"' - Timeout logs from API services
            start (str): Start timestamp in ISO 8601 format (e.g., '2024-01-15T10:00:00Z').
                        Should align with incident timeline for relevant log collection.
            end (str): End timestamp in ISO 8601 format (e.g., '2024-01-15T12:00:00Z').
                      Should cover the incident duration plus buffer time for context.
            limit (int, optional): Maximum number of log entries to retrieve.
                                  Range: 1-1000, Default: 100.
                                  Use 100-500 for focused analysis, 500-1000 for comprehensive investigation.
            direction (str, optional): Log retrieval direction:
                                     - "backward" (default): Most recent logs first, ideal for current issues
                                     - "forward": Chronological order, better for timeline analysis

    Returns:
        LogsResponse: A structured response object containing:
            - logs (List[dict]): Retrieved log entries with timestamps, levels, and messages
            - total_count (int): Total number of matching log entries
            - query_stats (dict): Query execution statistics and performance metrics
            - time_range (dict): Actual time range covered by the results

    Raises:
        ValueError: If query syntax is invalid or time range parameters are malformed
        TimeoutError: If log query execution exceeds timeout limits
        ServiceError: If Loki service is unavailable or returns errors
        Exception: For other unexpected errors during log retrieval

    Example:
        >>> # Fetch API error logs during incident timeframe
        >>> params = {
        ...     "query": '{job="api-service"} |= "ERROR"',
        ...     "start": "2024-01-15T14:00:00Z",
        ...     "end": "2024-01-15T15:00:00Z",
        ...     "limit": 200
        ... }
        >>> logs = await fetch_logs(params, tool_context)
        >>> print(f"Found {len(logs.logs)} error entries")

        >>> # Fetch database connection logs with regex pattern
        >>> params = {
        ...     "query": '{job="database"} |~ "connection.*failed"',
        ...     "start": "2024-01-15T13:30:00Z",
        ...     "end": "2024-01-15T14:30:00Z"
        ... }
        >>> logs = await fetch_logs(params, tool_context)

    Usage Guidelines:
        - Align time ranges with incident timeline for relevant log collection
        - Use specific job labels to focus on affected services
        - Combine label selectors with log filters for precise results
        - Start with broader queries, then narrow down based on findings
        - Consider log volume when setting limit values
        - Use backward direction for current issues, forward for timeline analysis
        - Store results in tool_context.state['logs'] for subsequent analysis
    """
    try:
        if "query" not in params or not params["query"]:
            # Default query to fetch all logs if no filter is provided
            params["query"] = '{job=~".+"}'
        tool_context.state["logs"] = await service.fetch_logs_from_loki(
            LogQueryParams(**params)
        )
        return tool_context.state["logs"]
    except Exception as e:
        raise


async def generate_log_summary(tool_context: ToolContext) -> str:
    """Generates an intelligent, human-readable summary of logs stored in the tool context.

    This function analyzes log entries previously fetched and stored in tool_context.state['logs']
    to create a concise, actionable summary. It identifies key patterns, error trends, and
    significant events that are relevant for incident investigation and troubleshooting.

    Args:
        tool_context (ToolContext): The tool execution context containing:
            - state['logs']: Previously fetched log data from fetch_logs function
                           Must contain log entries with timestamps, levels, and messages

    Returns:
        str: A comprehensive log summary containing:
            - Key error patterns and their frequency
            - Timeline of significant events
            - Service/component impact analysis
            - Notable anomalies or unusual patterns
            - Actionable insights for incident investigation

    Raises:
        KeyError: If tool_context.state['logs'] is not present or empty
        ValueError: If log data format is invalid or corrupted
        ServiceError: If AI summarization service is unavailable
        Exception: For other unexpected errors during summary generation

    Example:
        >>> # After fetching logs with fetch_logs
        >>> summary = await generate_log_summary(tool_context)
        >>> print(summary)
        "Log Summary (2024-01-15 14:00-15:00):
        - 47 database connection errors starting at 14:23 UTC
        - Connection pool exhaustion pattern detected
        - Peak error rate: 12 errors/minute at 14:25 UTC
        - Services affected: api-service, user-service, payment-service
        - Recommendation: Investigate database connection pool configuration"

    Usage Guidelines:
        - Ensure logs are fetched and stored in tool_context before calling
        - Use summary to identify key patterns before detailed log analysis
        - Combine with other analysis tools for comprehensive incident investigation
        - Review summary for actionable insights and investigation priorities
        - Use summary content to guide further log queries and analysis
    """
    response = completion(
        model="gemini/gemini-2.0-flash",
        messages=[
            {
                "role": "user",
                "content": f"Please provide a concise and accurate summary of the following logs:\n\n{tool_context.state['logs']}",
            }
        ],
    )
    if response and response.choices and response.choices[0].message.content:
        return response.choices[0].message.content.strip()
    return "No summary available."


async def generate_log_insights(tool_context: ToolContext) -> list[dict]:
    """
    Generate insights from the logs provided in the tool_context.state['logs'] and return result as an array of jsons with keys(title, description, logs)
    1. title: title of the insight
    2. description: description of the insight
    3. type: type of the insight (e.g., error, warning, info)
    4. logs: subset of logs from tool_context.state['logs'] that derives this

    The insights may be related to errors, warnings, or any significant events in the logs. Or it may be something affecting the system performance or security.
    It can also include patterns, anomalies, or trends observed in the logs that could be useful for further analysis or investigation.
    The insights should be concise, accurate, and focused on helping users quickly understand what the logs contain and identify any critical issues.
    """
    content = f"""
    Please provide a concise and accurate insights of the following logs:
    {tool_context.state["logs"]}

    The result should be an array of jsons with keys(title, description, logs). this is the format of result:
    {
        [
            {
                "title": "title of the insight",
                "description": "description of the insight",
                "type": "type of the insight",
                "logs": "subset of logs from {tool_context.state['logs']} that derives this insight",
            }
        ]
    }
    the final response should exactly be in this format
    """
    response = completion(
        model="gemini/gemini-2.0-flash",
        messages=[
            {
                "role": "user",
                "content": content,
            }
        ],
    )
    if response and response.choices and response.choices[0].message.content:
        try:
            json_string = response.choices[0].message.content.strip()
            pattern = r"^```json\s*(.*?)\s*```$"
            cleaned_string = re.sub(pattern, r"\1", json_string, flags=re.DOTALL)
            json_data = json.loads(cleaned_string.strip())
            if isinstance(json_data, list):
                return json_data
            else:
                return [
                    {
                        "title": "Error",
                        "description": "Insights should be an array of JSON objects.",
                    }
                ]

        except Exception as e:
            return [
                {"title": "Error", "description": f"Failed to parse insights: {str(e)}"}
            ]

    return [{"title": "Error", "description": "No insights available"}]


async def analyze_security_events(
    log_data: LogsResponse, tool_context: ToolContext
) -> dict:
    """Analyzes security events in logs to identify potential threats.

    Args:
        log_data: The log data to analyze.
        tool_context: Automatically provided by ADK, do not specify when calling.

    Returns:
        dict: A dictionary containing:
            - "status": "success" or "error"
            - If success: "findings" containing security analysis results
            - If error: "error_message" explaining what went wrong
    """
    # Implementation would analyze logs for security patterns
    # This is a placeholder
    return {"status": "success", "findings": "No suspicious security events detected"}
