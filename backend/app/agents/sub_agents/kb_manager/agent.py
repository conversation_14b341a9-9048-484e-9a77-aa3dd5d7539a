from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm

from . import prompt, tools

AGENT_MODEL = "gemini/gemini-2.0-flash"


kb_manager_agent = Agent(
    name="kb_manager",
    model=LiteLlm(AGENT_MODEL),
    description="Manages knowledge base and provides relevant documentation and insights.",
    instruction=prompt.INSTRUCTION,
    tools=[tools.find_relevant_documentation],
)
