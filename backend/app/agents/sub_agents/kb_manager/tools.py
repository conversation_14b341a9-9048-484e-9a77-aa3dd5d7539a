from typing import List

from utils.logger import get_service_logger
from vector_db.search_service import VectorSearchService

logger = get_service_logger(__name__)


def find_relevant_documentation(keywords: List[str], limit: int = 5) -> List[dict]:
    """Finds relevant documentation based on the provided keywords.

    Args:
        keywords: List of keywords to search for in the documentation
        limit: Maximum number of documents to return (default: 5)

    Returns:
        List[dict]: A list of dictionaries containing documentation details.
    """

    try:
        search_service = VectorSearchService()
        similar_documents = search_service.search_similar_documents_by_text(
            text=" ".join(keywords), top_k=limit
        )

        return similar_documents

    except Exception as e:
        logger.error(f"Error finding relevant documentation: {e}")
        return []
