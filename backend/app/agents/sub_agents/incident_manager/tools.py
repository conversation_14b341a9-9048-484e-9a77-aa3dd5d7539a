import os
from contextlib import contextmanager
from typing import List, Optional
from uuid import UUID

from database.core import get_db
from db_services import incident as incident_db_service
from entities.incident import Incident
from vector_db.search_service import VectorSearchService


@contextmanager
def get_db_session():
    db = next(get_db())
    try:
        yield db
    finally:
        db.close()


def get_recent_incidents(limit: int) -> List[dict]:
    """Retrieves a chronologically ordered list of recent incidents from the database.

    This function provides access to the most recent incidents in the system, ordered by
    creation time (most recent first). It's primarily used for initial context gathering during incident investigation.

    Args:
        limit (int): Maximum number of incidents to retrieve. Must be between 1 and 50.

    Returns:
        List[dict]: A list of incident summary dictionaries, each containing:
            - incident_id (str): Unique UUID identifier for database operations
            - incident_number (str): Human-readable incident reference (e.g., "INC-2024-0123")
            - title (str): Brief, descriptive incident title
            - summary (str): Detailed incident description and impact summary
            - status (str): Current incident status (DETECTED, TRIAGED, INVESTIGATING, RESOLVING, RESOLVED, CLOSED)
            - reported_at (str): ISO format timestamp when incident was first reported
            - updated_at (str): ISO format timestamp of last incident modification

    Raises:
        ValueError: If limit is not within valid range (1-50)
        DatabaseError: For database connection or query execution errors
        Exception: For other unexpected errors during incident retrieval

    Example:
        >>> recent_incidents = get_recent_incidents(limit=5)
        >>> for incident in recent_incidents:
        ...     print(f"{incident['incident_number']}: {incident['title']} ({incident['status']})")
        INC-2024-0123: API Latency Spike (INVESTIGATING)
        INC-2024-0122: Database Connection Issues (RESOLVED)
    """
    try:
        print(f"Getting recent incidents from {os.getenv('DATABASE_URL')}")
        with get_db_session() as db:
            incidents = incident_db_service.get_recent_incidents(db, limit)
            return [
                {
                    "incident_id": str(incident.id),
                    "incident_number": incident.incident_number,
                    "title": incident.title,
                    "summary": incident.summary,
                    "status": incident.status.value if incident.status else None,
                    "reported_at": (
                        incident.reported_at.isoformat()
                        if incident.reported_at
                        else None
                    ),
                    "updated_at": (
                        incident.updated_at.isoformat() if incident.updated_at else None
                    ),
                }
                for incident in incidents
            ]
    except Exception as e:
        print(f"Unexpected error retrieving recent incidents: {str(e)}")
        raise Exception(f"Failed to retrieve recent incidents: {str(e)}") from e


def get_incident_details(
    incident_id: Optional[str] = None,
    incident_number: Optional[str] = None,
) -> dict:
    """Retrieves comprehensive details for a specific incident using either UUID or incident number.

    This function provides complete incident information including metadata, timeline, impact assessment,
    and resolution details. It supports flexible querying using either the internal UUID or the
    human-readable incident number, making it suitable for both programmatic access and user queries.

    Args:
        incident_id (str, optional): The UUID of the incident to retrieve. Must be a valid UUID format.
                                   Not required if incident_number is provided.
        incident_number (str, optional): The human-readable incident number (e.g., "INC-2024-0123", "github-456").
                                       Not required if incident_id is provided.

    Returns:
        dict: A comprehensive incident details dictionary containing:
            - incident_id (str): Unique UUID identifier
            - incident_number (str): Human-readable incident reference
            - title (str): Brief incident description
            - summary (str): Detailed incident summary and impact description
            - status (str): Current incident status
            - priority (str): Incident priority level (P0, P1, P2, P3, P4)
            - severity (str): Impact severity assessment
            - incident_type (str): Classification of incident type
            - affected_services (List[str]): List of impacted services/systems
            - reported_at (str): ISO timestamp when incident was first reported
            - updated_at (str): ISO timestamp of last modification
            - resolution_time (str, optional): ISO timestamp when incident was resolved
            - root_cause (str, optional): Identified root cause if determined
            - resolution_summary (str, optional): Summary of resolution actions taken

    Raises:
        ValueError: If neither incident_id nor incident_number is provided, if incident_id
                   is not a valid UUID format, or if the specified incident is not found
        DatabaseError: For database connection or query execution errors
        Exception: For other unexpected errors during incident retrieval

    Example:
        >>> # Query by incident number (most common)
        >>> incident = get_incident_details(incident_number="INC-2024-0123")
        >>> print(f"Status: {incident['status']}, Priority: {incident['priority']}")

        >>> # Query by UUID (programmatic access)
        >>> incident = get_incident_details(incident_id="550e8400-e29b-41d4-a716-************")
        >>> print(f"Affected services: {incident['affected_services']}")

    Usage Guidelines:
        - Prefer incident_number for user-facing queries and reports
        - Use incident_id for programmatic access and database operations
        - Check incident status to determine if investigation is ongoing
        - Review affected_services to understand impact scope
        - Use timeline information (reported_at, updated_at) for analysis
    """
    if not incident_id and not incident_number:
        raise ValueError("Must specify either incident_id or incident_number")

    try:
        print(f"Getting incident details from {os.getenv('DATABASE_URL')}")
        with get_db_session() as db:
            if incident_number:
                incident = incident_db_service.get_incident_by_number(
                    db, incident_number
                )
            elif incident_id:
                try:
                    incident_uuid = UUID(incident_id)
                except ValueError as e:
                    raise ValueError(
                        f"Invalid incident_id format: {incident_id}"
                    ) from e

                incident = incident_db_service.get_incident_by_id(db, incident_uuid)
            if not incident:
                raise ValueError(f"Incident not found with id: {incident_id}")

            result = {
                "incident_id": str(incident.id),
                "incident_number": incident.incident_number,
                "title": incident.title,
                "summary": incident.summary,
                "priority": incident.priority.value if incident.priority else None,
                "severity": incident.severity.value if incident.severity else None,
                "incident_type": (
                    incident.incident_type.value if incident.incident_type else None
                ),
                "status": incident.status.value if incident.status else None,
                "reported_at": (
                    incident.reported_at.isoformat() if incident.reported_at else None
                ),
                "updated_at": (
                    incident.updated_at.isoformat() if incident.updated_at else None
                ),
            }

            return result

    except ValueError:
        raise
    except Exception as e:
        print(f"Unexpected error retrieving incident details: {str(e)}")
        raise Exception(f"Failed to retrieve incident details: {str(e)}") from e


def get_similar_incidents(incident_id: str, top_k: int = 5) -> List[dict]:
    """Finds historically similar incidents using intelligent vector similarity search.

    This function leverages advanced vector similarity matching to identify past incidents
    that share similar characteristics, symptoms, or patterns with the specified incident.
    It's particularly valuable for leveraging historical knowledge, identifying proven
    resolution strategies, and understanding common failure patterns.

    Args:
        incident_id (str): UUID string of the incident to find matches for. Must be a valid
                          UUID format and correspond to an existing incident in the database.
        top_k (int, optional): Maximum number of similar incidents to return. Must be between
                              1 and 20. Defaults to 5 for focused analysis. Use higher values
                              (10-20) for comprehensive pattern analysis.

    Returns:
        List[dict]: A ranked list of similar incidents, ordered by similarity score (highest first).
                   Each dictionary contains:
            - incident_id (str): UUID of the similar incident
            - incident_number (str): Human-readable incident reference
            - title (str): Brief incident description
            - similarity_score (float): Relevance score between 0.0-1.0 (higher = more similar)
            - resolution_summary (str, optional): How the incident was resolved
            - root_cause (str, optional): Identified root cause if determined
            - affected_services (List[str]): Services impacted by the incident
            - status (str): Final incident status
            - resolved_at (str, optional): ISO timestamp when incident was resolved

    Raises:
        ValueError: If incident_id is not a valid UUID format, if top_k is not within
                   valid range (1-20), or if the specified incident is not found
        VectorSearchError: For vector similarity search service errors
        DatabaseError: For database connection or query execution errors
        Exception: For other unexpected errors during similarity search

    Example:
        >>> # Find similar incidents for analysis
        >>> similar = get_similar_incidents("550e8400-e29b-41d4-a716-************", top_k=3)
        >>> for incident in similar:
        ...     print(f"{incident['incident_number']}: {incident['similarity_score']:.2f} - {incident['title']}")
        INC-2024-0098: 0.87 - Database Connection Pool Exhaustion
        INC-2024-0076: 0.82 - API Latency Spike During Peak Hours
        INC-2024-0054: 0.78 - PostgreSQL Performance Degradation

        >>> # Check for resolution strategies
        >>> if similar[0]['resolution_summary']:
        ...     print(f"Previous resolution: {similar[0]['resolution_summary']}")

    Usage Guidelines:
        - Focus on incidents with similarity scores > 0.7 for most relevant matches
        - Review resolution_summary and root_cause from similar incidents for guidance
        - Use top_k=3-5 for focused analysis, 10-20 for comprehensive pattern recognition
        - Consider affected_services overlap when evaluating similarity relevance
        - Prioritize resolved incidents with clear resolution strategies
        - Use historical context to inform current incident investigation approach
    """
    try:
        with get_db_session() as db:
            incident_uuid = UUID(incident_id)
            # Get the incident to find similar ones for
            incident = db.query(Incident).filter(Incident.id == incident_uuid).first()
            if not incident:
                return []

            # Initialize vector search service
            search_service = VectorSearchService()

            # Get similar incidents using vector search
            similar_incidents = search_service.search_similar_incidents_by_id(
                db=db, incident_id=incident_uuid, top_k=top_k
            )

            return similar_incidents

    except Exception as e:
        return []
