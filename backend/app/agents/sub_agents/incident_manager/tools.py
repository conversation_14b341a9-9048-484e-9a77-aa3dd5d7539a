import os
from contextlib import contextmanager
from typing import List, Optional
from uuid import UUID

from database.core import get_db
from db_services import incident as incident_db_service
from entities.incident import Incident
from vector_db.search_service import VectorSearchService


@contextmanager
def get_db_session():
    db = next(get_db())
    try:
        yield db
    finally:
        db.close()


def get_recent_incidents(limit: int) -> List[dict]:
    """Retrieves a list of recent incidents with optional filtering by number of incidents to retrieve.

    Args:
        limit: Maximum number of incidents to retrieve, defaults to 10.

    Returns:
        List[dict]: A list of dictionaries containing incident details.

    Raises:
        Exception: For database or other unexpected errors.
    """
    try:
        print(f"Getting recent incidents from {os.getenv('DATABASE_URL')}")
        with get_db_session() as db:
            incidents = incident_db_service.get_recent_incidents(db, limit)
            return [
                {
                    "incident_id": str(incident.id),
                    "incident_number": incident.incident_number,
                    "title": incident.title,
                    "summary": incident.summary,
                    "status": incident.status.value if incident.status else None,
                    "reported_at": (
                        incident.reported_at.isoformat()
                        if incident.reported_at
                        else None
                    ),
                    "updated_at": (
                        incident.updated_at.isoformat() if incident.updated_at else None
                    ),
                }
                for incident in incidents
            ]
    except Exception as e:
        print(f"Unexpected error retrieving recent incidents: {str(e)}")
        raise Exception(f"Failed to retrieve recent incidents: {str(e)}") from e


def get_incident_details(
    incident_id: Optional[str],
    incident_number: Optional[str],
) -> dict:
    """Retrieves the details of a specific incident by incident_id or incident_number.

    Args:
        incident_id: The UUID of the incident to retrieve. If incident_id is not provided, incident_number must be provided.
        incident_number: The incident number of the incident to retrieve. If incident_number is not provided, incident_id must be provided.

    Returns:
        dict: A dictionary containing the incident details.

    Raises:
        ValueError: If neither incident_id nor incident_number is provided,
                   or if the incident is not found.
        Exception: For database or other unexpected errors.
    """
    if not incident_id and not incident_number:
        raise ValueError("Must specify either incident_id or incident_number")

    try:
        print(f"Getting incident details from {os.getenv('DATABASE_URL')}")
        with get_db_session() as db:
            if incident_number:
                incident = incident_db_service.get_incident_by_number(
                    db, incident_number
                )
            elif incident_id:
                try:
                    incident_uuid = UUID(incident_id)
                except ValueError as e:
                    raise ValueError(
                        f"Invalid incident_id format: {incident_id}"
                    ) from e

                incident = incident_db_service.get_incident_by_id(db, incident_uuid)
            if not incident:
                raise ValueError(f"Incident not found with id: {incident_id}")

            result = {
                "incident_id": str(incident.id),
                "incident_number": incident.incident_number,
                "title": incident.title,
                "summary": incident.summary,
                "priority": incident.priority.value if incident.priority else None,
                "severity": incident.severity.value if incident.severity else None,
                "incident_type": (
                    incident.incident_type.value if incident.incident_type else None
                ),
                "status": incident.status.value if incident.status else None,
                "reported_at": (
                    incident.reported_at.isoformat() if incident.reported_at else None
                ),
                "updated_at": (
                    incident.updated_at.isoformat() if incident.updated_at else None
                ),
            }

            return result

    except ValueError:
        raise
    except Exception as e:
        print(f"Unexpected error retrieving incident details: {str(e)}")
        raise Exception(f"Failed to retrieve incident details: {str(e)}") from e


def get_similar_incidents(incident_id: str, top_k: int = 5):
    """
    Find similar incidents using vector similarity search.

    Args:
        incident_id: UUID string of the incident to find matches for
        top_k: Number of similar incidents to return

    Returns:
        List[dict]: Similar incidents with their IDs, similarity scores, and basic info

    """
    try:
        with get_db_session() as db:
            incident_id = UUID(incident_id)
            # Get the incident to find similar ones for
            incident = db.query(Incident).filter(Incident.id == incident_id).first()
            if not incident:
                return []

            # Initialize vector search service
            search_service = VectorSearchService()

            # Get similar incidents using vector search
            similar_incidents = search_service.search_similar_incidents(
                incident_id=str(incident_id), top_k=top_k
            )

            return similar_incidents

    except Exception as e:
        return []
