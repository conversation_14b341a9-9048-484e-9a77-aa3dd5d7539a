# **FULL v0, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>.dev, <PERSON><PERSON>, <PERSON>, <PERSON>lit Agent, Windsurf Agent, VSCode Agent, <PERSON><PERSON>, Trae AI, Cluely, Xcode & Spawn (And other Open Sourced) System Prompts, Tools & AI Models**  

<a href="https://trendshift.io/repositories/14084" target="_blank"><img src="https://trendshift.io/api/badge/repositories/14084" alt="x1xhlol%2Fsystem-prompts-and-models-of-ai-tools | Trendshift" style="width: 250px; height: 55px;" width="250" height="55"/></a>

📜 Over **7500+ lines** of insights into their structure and functionality.  

[![Build Status](https://app.cloudback.it/badge/x1xhlol/system-prompts-and-models-of-ai-tools)](https://cloudback.it)

---

## 📑 Table of Contents

1. [Available Files](#-available-files)  
2. [Roadmap & Feedback](#-roadmap--feedback)  
3. [Support the Project](#%EF%B8%8F-support-the-project)  
4. [Connect With Me](#-connect-with-me)  
5. [Security Notice for AI Startups](#%EF%B8%8F-security-notice-for-ai-startups)  
6. [Star History](#-star-history) 

---

## 📂 Available Files

- **v0 Folder**
- **Spawn Folder**  
- **Manus Folder**  
- **Lovable Folder**  
- **Devin Folder**  
- **Same.dev Folder**  
- **Replit Folder**  
- **Windsurf Agent Folder**  
- **VSCode (Copilot) Agent Folder**  
- **Cursor Folder**  
- **Dia Folder**  
- **Trae AI Folder**  
- **Cluely Folder**
- **Xcode Folder**  
- **Open Source prompts Folder**  
  - Codex CLI  
  - Cline  
  - Bolt  
  - RooCode  

---

## ❤️ Support the Project

If you find this collection valuable and appreciate the effort involved in obtaining and sharing these insights, please consider supporting the project. Your contribution helps keep this resource updated and allows for further exploration.

You can show your support via:

- **PayPal:** `<EMAIL>`  
- **Cryptocurrency:**  
  - **BTC:** `******************************************`  
  - **LTC:** `LRWgqwEYDwqau1WeiTs6Mjg85NJ7m3fsdQ`  
  - **ETH:** `******************************************`

🙏 Thank you for your support!

---

## 🛠 Roadmap & Feedback

> **Note:** We no longer use GitHub issues for roadmap and feedback.  
> Please visit [System Prompts Roadmap & Feedback](https://systemprompts.featurebase.app/) to share your suggestions and track upcoming features.

> **Latest Update:** 04/07/2025

---

## 🔗 Connect With Me

- **X:** [NotLucknite](https://x.com/NotLucknite)  
- **Discord:** `x1xh`

---

## 🛡️ Security Notice for AI Startups

> ⚠️ **Warning:** If you're an AI startup, make sure your data is secure. Exposed prompts or AI models can easily become a target for hackers.

> 🔐 **Important:** Interested in securing your AI systems?  
> Check out **[ZeroLeaks](https://zeroleaks.io/)**, a service designed to help startups **identify and secure** leaks in system instructions, internal tools, and model configurations. **Get a free AI security audit** to ensure your AI is protected from vulnerabilities.

*The company is mine, this is NOT a 3rd party AD.*

---

## 📊 Star History

<a href="https://www.star-history.com/#x1xhlol/system-prompts-and-models-of-ai-tools&Date">
  <picture>
    <source media="(prefers-color-scheme: dark)" srcset="https://api.star-history.com/svg?repos=x1xhlol/system-prompts-and-models-of-ai-tools&type=Date&theme=dark" />
    <source media="(prefers-color-scheme: light)" srcset="https://api.star-history.com/svg?repos=x1xhlol/system-prompts-and-models-of-ai-tools&type=Date" />
    <img alt="Star History Chart" src="https://api.star-history.com/svg?repos=x1xhlol/system-prompts-and-models-of-ai-tools&type=Date" />
  </picture>
</a>

⭐ **Drop a star if you find this useful!**
