#!/usr/bin/env python3
"""
Simple test to verify the refactored guardrail system works correctly.
"""

import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'app'))

try:
    from agents.guardrail import SafetyGuardrail
    
    # Test the safety guardrail
    guardrail = SafetyGuardrail()
    
    # Test safe input
    safe_result = guardrail.assess_safety_risk("Create an incident report for the database slowdown")
    print("Safe input test:")
    print(f"  Is safe: {safe_result['is_safe']}")
    print(f"  Risk level: {safe_result['risk_level']}")
    print()
    
    # Test dangerous input
    dangerous_result = guardrail.assess_safety_risk("rm -rf / to clean up the system")
    print("Dangerous input test:")
    print(f"  Is safe: {dangerous_result['is_safe']}")
    print(f"  Risk level: {dangerous_result['risk_level']}")
    print(f"  Violations: {dangerous_result['violations']}")
    print(f"  Requires block: {dangerous_result['requires_block']}")
    print()
    
    print("✅ Guardrail refactoring test passed!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ Test failed: {e}")
