CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- UserRole Enum
DO $$ BEGIN
    CREATE TYPE userroleenum AS ENUM ('ENGINEER', 'MANAGER', 'ADMIN');
EXCEPTION WHEN duplicate_object THEN null;
END $$;

-- Severity Enum
DO $$ BEGIN
    CREATE TYPE severityenum AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');
EXCEPTION WHEN duplicate_object THEN null;
END $$;

-- Priority Enum
DO $$ BEGIN
    CREATE TYPE priorityenum AS ENUM ('P0', 'P1', 'P2', 'P3', 'P4');
EXCEPTION WHEN duplicate_object THEN null;
END $$;

-- Incident Type Enum
DO $$ BEGIN
    CREATE TYPE incidenttypeenum AS ENUM ('OUTAGE', 'DEGRADATION', 'PERFORMANCE', 'SECURITY', 'OTHER');
EXCEPTION WHEN duplicate_object THEN null;
END $$;

-- Status Enum
DO $$ BEGIN
    CREATE TYPE statusenum AS ENUM ('OPEN', 'ACTIVE', 'RESOLVED', 'CLOSED');
EXCEPTION WHEN duplicate_object THEN null;
END $$;

-- Runbook Type Enum
DO $$ BEGIN
    CREATE TYPE runbooktypeenum AS ENUM ('TROUBLESHOOT', 'ROLLBACK', 'MITIGATION', 'RECOVERY', 'OTHER');
EXCEPTION WHEN duplicate_object THEN null;
END $$;

-- Step Status Enum
DO $$ BEGIN
    CREATE TYPE stepstatusenum AS ENUM ('PENDING','SUCCESSFUL', 'FAILED' );
EXCEPTION WHEN duplicate_object THEN null;
END $$;

-- Event Type Enum
DO $$ BEGIN
    DROP TYPE IF EXISTS eventtypeenum CASCADE;
    CREATE TYPE eventtypeenum AS ENUM (
        'CREATED',
        'UPDATED',
        'ESCALATED',
        'RESOLVED',
        'CLOSED',
        'COMMENT_ADDED',
        'ASSIGNED',
        'PRIORITY_CHANGED',
        'SEVERITY_CHANGED',
        'ANALYSIS'
    );
EXCEPTION WHEN duplicate_object THEN null;
END $$;

-- Users Table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY,
    email VARCHAR UNIQUE NOT NULL,
    first_name VARCHAR NOT NULL,
    last_name VARCHAR NOT NULL,
    password VARCHAR NOT NULL,
    role userroleenum NOT NULL DEFAULT 'ENGINEER',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Sample Users
INSERT INTO users (id, email, first_name, last_name, password, role) VALUES
    ('18ebc4a2-45e3-46d6-b632-4a9dba74bcb2', '<EMAIL>', 'user', 'example', '$2b$12$v9C2Wz6SarwbLz0Uft19J.VuoATFxfL9rkNSJ4kT.LxQJeGOZiYdy', 'ADMIN'),
    ('11111111-1111-1111-1111-111111111111', '<EMAIL>', 'Robert', 'Wolfkisser', 'password123', 'ENGINEER'),
    ('*************-2222-2222-************', '<EMAIL>', 'Jill', 'Jailbreaker', 'password123', 'ENGINEER'),
    ('*************-3333-3333-************', '<EMAIL>', 'Henry', 'Silkeater', 'password123', 'ENGINEER'),
    ('*************-4444-4444-************', '<EMAIL>', 'Bill', 'Horsefighter', 'password123', 'ENGINEER'),
    ('*************-5555-5555-555555555555', '<EMAIL>', 'Jeremy', 'Footviewer', 'password123', 'ENGINEER'),
    ('66666666-6666-6666-6666-666666666666', '<EMAIL>', 'Sarah', 'Johnson', 'password123', 'ENGINEER'),
    ('*************-7777-7777-************', '<EMAIL>', 'Michael', 'Chen', 'password123', 'ENGINEER'),
    ('*************-8888-8888-************', '<EMAIL>', 'Emily', 'Davis', 'password123', 'ENGINEER'),
    ('*************-9999-9999-************', '<EMAIL>', 'David', 'Brown', 'password123', 'ENGINEER'),
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '<EMAIL>', 'Jessica', 'White', 'password123', 'ENGINEER');

-- Incidents Table
CREATE TABLE IF NOT EXISTS incidents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    incident_number TEXT UNIQUE NOT NULL,
    title TEXT NOT NULL,
    summary TEXT,
    priority priorityenum,
    severity severityenum,
    incident_type incidenttypeenum NOT NULL,
    status statusenum NOT NULL,
    reported_by UUID REFERENCES users(id) ON DELETE SET NULL,
    reported_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Incident Details Table
CREATE TABLE IF NOT EXISTS incident_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    incident_id UUID NOT NULL UNIQUE REFERENCES incidents(id) ON DELETE CASCADE,
    affected_services JSON DEFAULT '[]',
    tags JSON DEFAULT '[]',
    incident_details TEXT,
    attachments JSON DEFAULT '[]',
    -- AI Analysis fields
    root_cause TEXT,
    immediate_action TEXT,
    impact_forecast TEXT,
    cascading_risks TEXT
);

-- Insert Incidents
INSERT INTO incidents (
    incident_number, title, summary, priority, severity, incident_type,
    status, reported_by, reported_at
) VALUES
('INC-001', 'Service Outage - API Gateway', 'External API endpoints unreachable, affecting client applications', 'P1',
 'HIGH', 'OUTAGE', 'OPEN', '11111111-1111-1111-1111-111111111111', NOW() - INTERVAL '2 hours'),

('INC-002', 'Database Connection Failure', 'Primary database cluster experiencing connection timeouts', 'P0',
 'CRITICAL', 'OUTAGE', 'ACTIVE', '*************-2222-2222-************', NOW() - INTERVAL '45 minutes'),

('INC-003', 'Authentication Service Degradation', 'Increased latency in auth service affecting login times', 'P2',
 'MEDIUM', 'PERFORMANCE', 'ACTIVE', '*************-3333-3333-************', NOW() - INTERVAL '1 day'),

('INC-004', 'UI Rendering Issue - Dashboard', 'Charts not displaying correctly in analytics dashboard', 'P3',
 'LOW', 'OTHER', 'RESOLVED', '*************-4444-4444-************', NOW() - INTERVAL '2 days'),

('INC-005', 'Memory Leak - Backend Service', 'Gradual memory growth observed in user service', 'P1',
 'HIGH', 'PERFORMANCE', 'CLOSED', '*************-5555-5555-555555555555', NOW() - INTERVAL '3 days'),

('INC-006', 'SSL Certificate Expiration', 'Production certificate expires in less than 24 hours', 'P0',
 'CRITICAL', 'SECURITY', 'OPEN', '66666666-6666-6666-6666-666666666666', NOW() - INTERVAL '30 minutes'),

('INC-007', 'Network Connectivity Issues', 'Intermittent packet loss between data centers', 'P1',
 'HIGH', 'OUTAGE', 'ACTIVE', '*************-7777-7777-************', NOW() - INTERVAL '1 hour'),

('INC-008', 'Disk Space Exhaustion - Primary Storage', 'Primary storage disk space is 95% full', 'P2',
 'MEDIUM', 'PERFORMANCE', 'RESOLVED', '*************-8888-8888-************', NOW() - INTERVAL '2 hours'),

('INC-009', 'Database Query Timeout', 'Long running queries causing timeouts in user service', 'P3',
 'LOW', 'PERFORMANCE', 'CLOSED', '*************-9999-9999-************', NOW() - INTERVAL '4 days'),

('INC-010', 'API Rate Limiting', 'API rate limiting causing 503 errors for clients', 'P2',
 'MEDIUM', 'PERFORMANCE', 'ACTIVE', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', NOW() - INTERVAL '1 day');

-- Insert matching Incident Details
INSERT INTO incident_details (
    incident_id, affected_services, tags, incident_details, attachments
)
SELECT id,
       CASE incident_number
           WHEN 'INC-001' THEN '["API Gateway"]'
           WHEN 'INC-002' THEN '["Database"]'
           WHEN 'INC-003' THEN '["Auth Service"]'
           WHEN 'INC-004' THEN '["Dashboard"]'
           WHEN 'INC-005' THEN '["User Service"]'
           WHEN 'INC-006' THEN '["SSL/TLS"]'
           WHEN 'INC-007' THEN '["Network"]'
           WHEN 'INC-008' THEN '["Primary Storage"]'
           WHEN 'INC-009' THEN '["Database"]'
           WHEN 'INC-010' THEN '["API Gateway"]'
       END::json,
       '[]',
       CASE incident_number
           WHEN 'INC-001' THEN 'External API endpoints unreachable, affecting client applications. Load balancer configuration issue identified.'
           WHEN 'INC-002' THEN 'Primary database cluster experiencing connection timeouts. Connection pool optimization required.'
           WHEN 'INC-003' THEN 'Increased latency in auth service affecting login times. Cache optimization implemented.'
           WHEN 'INC-004' THEN 'Charts not displaying correctly in analytics dashboard. Library update required.'
           WHEN 'INC-005' THEN 'Gradual memory growth observed in user service. Memory leak identified and fixed.'
           WHEN 'INC-006' THEN 'Production certificate expires in less than 24 hours. Certificate renewal process initiated.'
           WHEN 'INC-007' THEN 'Intermittent packet loss between data centers. Network connectivity issues under investigation.'
           WHEN 'INC-008' THEN 'Primary storage disk space is 95% full. Disk cleanup and expansion required.'
           WHEN 'INC-009' THEN 'Long running queries causing timeouts in user service. Query optimization implemented.'
           WHEN 'INC-010' THEN 'API rate limiting causing 503 errors for clients. Rate limit configuration adjustment needed.'
       END,
       '[]'
FROM incidents
WHERE incident_number IN (
    'INC-001', 'INC-002', 'INC-003', 'INC-004', 'INC-005',
    'INC-006', 'INC-007', 'INC-008', 'INC-009', 'INC-010'
);

-- Runbooks Table
CREATE TABLE IF NOT EXISTS runbooks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    incident_id UUID NOT NULL REFERENCES incidents(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    type runbooktypeenum NOT NULL,
    purpose TEXT,
    details TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Runbook Steps Table
CREATE TABLE IF NOT EXISTS runbook_steps (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    runbook_id UUID NOT NULL REFERENCES runbooks(id) ON DELETE CASCADE,
    step_order INTEGER NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    details TEXT NOT NULL,
    expected_result TEXT NOT NULL,
    status stepstatusenum,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    executed_at TIMESTAMPTZ,
    executed_by UUID REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE (runbook_id, step_order)
);

-- Sample Runbooks
INSERT INTO runbooks (id, incident_id, title, type, purpose, details, created_at) VALUES
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaa0001',
     (SELECT id FROM incidents WHERE incident_number = 'INC-001'),
     'API Gateway Outage Troubleshooting',
     'TROUBLESHOOT',
     'Diagnose and mitigate the API Gateway outage issue.',
     'Follow the steps to identify root cause and restore service.',
     NOW() - INTERVAL '90 minutes'),

    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaa0002',
     (SELECT id FROM incidents WHERE incident_number = 'INC-002'),
     'Database Timeout Rollback',
     'ROLLBACK',
     'Roll back the DB changes that may have caused timeouts.',
     'This runbook covers DB config rollback procedure.',
     NOW() - INTERVAL '30 minutes');

-- Sample Runbook Steps for INC-001
INSERT INTO runbook_steps (runbook_id, step_order, title, description, details, expected_result, status, notes, executed_at, executed_by) VALUES
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaa0001', 1, 'Check API Gateway service health',
    'Verify the health of the API Gateway service.',
    'Check API Gateway service health via monitoring dashboard.',
    'Service health metrics are within normal range.',
     'SUCCESSFUL', 'Service was down.', NOW() - INTERVAL '85 minutes', (SELECT id FROM users WHERE email = '<EMAIL>')),
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaa0001', 2, 'Restart the API Gateway',
    'Restart the API Gateway container',
    'Restart the API Gateway container on production cluster.',
    'API Gateway service is up and running.',
     'SUCCESSFUL', 'Restart completed successfully.', NOW() - INTERVAL '80 minutes', (SELECT id FROM users WHERE email = '<EMAIL>')),
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaa0001', 3, 'Verify endpoint',
    'Verify endpoint accessibility',
    'Verify endpoint accessibility with curl or Postman.',
    'Endpoints are reachable now.',
    'PENDING', 'Endpoints are reachable now.', NOW() - INTERVAL '75 minutes', (SELECT id FROM users WHERE email = '<EMAIL>'));

-- Sample Runbook Steps for INC-002
INSERT INTO runbook_steps (runbook_id, step_order, title, description, details, expected_result, status, notes, executed_at, executed_by) VALUES
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaa0002', 1, 'Recent schema changes',
    'Identify recent schema changes',
    'Identify recent schema changes from migration logs.',
    'No recent schema changes from migration logs.',
    'SUCCESSFUL', 'Found two migrations pushed 45 minutes ago.', NOW() - INTERVAL '25 minutes', (SELECT id FROM users WHERE email = '<EMAIL>')),
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaa0002', 2, 'Revert migration',
    'Revert problematic migration',
    'Revert problematic migration using rollback script.',
    'Migration reverted successfully.',
    'FAILED', 'Rollback script failed due to dependency.', NOW() - INTERVAL '20 minutes', (SELECT id FROM users WHERE email = '<EMAIL>')),
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaa0002', 3, 'Restart Database',
    'Restart DB instances',
     'Restart DB instances to clear connections.',
     'DB instances restarted successfully.',
     'PENDING', 'Resolved timeout issue after restart.', NOW() - INTERVAL '15 minutes', (SELECT id FROM users WHERE email = '<EMAIL>'));

-- Events Table
CREATE TABLE IF NOT EXISTS incident_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_name VARCHAR NOT NULL,
    event_datetime TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    event_type eventtypeenum NOT NULL,
    event_details JSONB NOT NULL DEFAULT '{
        "description": "",
        "triggered_by": "",
        "status": "",
        "notes": "",
        "recommended_actions": [],
        "ai_analysis_confidence": 0
    }',
    user_id UUID REFERENCES users(id),
    incident_id UUID REFERENCES incidents(id),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_events_datetime ON incident_events(event_datetime DESC);
CREATE INDEX IF NOT EXISTS idx_events_incident_id ON incident_events(incident_id);

-- Insert events for each incident
INSERT INTO incident_events (event_name, event_type, event_details, user_id, incident_id, event_datetime) VALUES
-- Incident 1: API Gateway Outage (INC-001)
('Initial Alert: API Gateway Outage', 'CREATED',
'{
  "description": "Critical API Gateway outage detected affecting multiple services",
  "alert_details": {
    "source": "Monitoring System",
    "severity": "HIGH",
    "affected_components": ["API Gateway", "Authentication", "Client Services"],
    "impact_scope": "Global",
    "detection_method": "Automated Monitoring"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-001'),
NOW() - INTERVAL '5 hours'),

('SRE Team Acknowledgment', 'UPDATED',
'{
  "description": "SRE team has acknowledged the incident and begun investigation",
  "analysis_details": {
    "team": "SRE",
    "initial_findings": "High error rates in API endpoints",
    "priority_assessment": "P1",
    "next_steps": "Detailed system analysis"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-001'),
NOW() - INTERVAL '4 hours 45 minutes'),

('Root Cause Analysis', 'ANALYSIS',
'{
  "description": "Detailed analysis of API Gateway failure completed",
  "analysis_details": {
    "root_cause": "Load balancer configuration error",
    "affected_systems": ["API Gateway", "Load Balancer"],
    "impact_assessment": "Critical - 90% of API requests affected",
    "mitigation_plan": "Load balancer reconfiguration required"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-001'),
NOW() - INTERVAL '4 hours 30 minutes'),

('Mitigation Started', 'UPDATED',
'{
  "description": "Implementation of fix for load balancer configuration",
  "alert_details": {
    "action": "Load balancer reconfiguration",
    "status": "In Progress",
    "estimated_completion": "30 minutes",
    "impact": "Temporary increased latency during changes"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-001'),
NOW() - INTERVAL '4 hours 15 minutes'),

('Resolution Confirmed', 'RESOLVED',
'{
  "description": "API Gateway service fully restored and stable",
  "analysis_details": {
    "resolution_steps": "Load balancer reconfigured successfully",
    "verification": "All API endpoints responding normally",
    "recovery_metrics": {
      "error_rate": "0.1%",
      "latency": "normal",
      "success_rate": "99.9%"
    }
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-001'),
NOW() - INTERVAL '4 hours'),

-- Incident 2: Database Connection Failure (INC-002)
('Database Connectivity Alert', 'CREATED',
'{
  "description": "Multiple services experiencing database connection failures",
  "alert_details": {
    "source": "Database Monitoring",
    "severity": "CRITICAL",
    "affected_services": ["User Service", "Order Processing"],
    "connection_metrics": {
      "failure_rate": "85%",
      "latency": "high"
    }
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-002'),
NOW() - INTERVAL '3 hours'),

('DBA Team Response', 'UPDATED',
'{
  "description": "Database administration team engaged and investigating",
  "analysis_details": {
    "team": "DBA",
    "initial_assessment": "Connection pool exhaustion",
    "priority": "P0",
    "immediate_actions": ["Pool analysis", "Resource monitoring"]
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-002'),
NOW() - INTERVAL '2 hours 45 minutes'),

('Connection Pool Analysis', 'ANALYSIS',
'{
  "description": "Detailed analysis of database connection issues",
  "analysis_details": {
    "findings": {
      "pool_status": "Exhausted",
      "active_connections": "Maximum",
      "wait_time": "30s average"
    },
    "root_cause": "Connection leaks in application code"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-002'),
NOW() - INTERVAL '2 hours 30 minutes'),

('Fix Implementation', 'UPDATED',
'{
  "description": "Deploying connection pool optimizations",
  "alert_details": {
    "action": "Configuration update",
    "changes": ["Increased pool size", "Timeout adjustments"],
    "deployment_status": "In Progress"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-002'),
NOW() - INTERVAL '2 hours 15 minutes'),

('Database Service Restored', 'RESOLVED',
'{
  "description": "Database connectivity fully restored and stable",
  "analysis_details": {
    "resolution": "Connection pool optimized",
    "metrics": {
      "connection_success": "100%",
      "response_time": "normal",
      "pool_utilization": "65%"
    }
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-002'),
NOW() - INTERVAL '2 hours'),

-- Incident 3: Authentication Service Degradation (INC-003)
('Authentication Service Alert', 'CREATED',
'{
  "description": "Authentication service experiencing increased latency and timeouts",
  "alert_details": {
    "source": "Service Monitor",
    "severity": "HIGH",
    "affected_service": "Authentication",
    "metrics": {
      "latency": "5s average",
      "timeout_rate": "15%"
    }
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-003'),
NOW() - INTERVAL '8 hours'),

('Security Team Engagement', 'UPDATED',
'{
  "description": "Security team investigating authentication service degradation",
  "analysis_details": {
    "team": "Security",
    "initial_findings": "Cache performance issues",
    "affected_areas": ["Token validation", "Session management"],
    "priority": "P1"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-003'),
NOW() - INTERVAL '7 hours 45 minutes'),

('Performance Analysis Complete', 'ANALYSIS',
'{
  "description": "Detailed analysis of authentication service performance issues",
  "analysis_details": {
    "root_cause": "Redis cache memory pressure",
    "impact": "Global authentication slowdown",
    "affected_operations": ["Login", "Token refresh"],
    "recommended_action": "Cache optimization required"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-003'),
NOW() - INTERVAL '7 hours 30 minutes'),

('Cache Optimization', 'UPDATED',
'{
  "description": "Implementing cache optimization measures",
  "alert_details": {
    "action": "Cache reconfiguration",
    "changes": ["Memory allocation increase", "Eviction policy update"],
    "status": "In Progress"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-003'),
NOW() - INTERVAL '7 hours 15 minutes'),

('Service Recovery Confirmed', 'RESOLVED',
'{
  "description": "Authentication service performance restored to normal",
  "analysis_details": {
    "resolution": "Cache optimization successful",
    "metrics": {
      "latency": "200ms average",
      "success_rate": "99.9%",
      "cache_hit_rate": "95%"
    }
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-003'),
NOW() - INTERVAL '7 hours'),

-- Incident 4: UI Dashboard Issue (INC-004)
('Dashboard Rendering Alert', 'CREATED',
'{
  "description": "Analytics dashboard charts failing to render correctly",
  "alert_details": {
    "source": "User Reports",
    "severity": "MEDIUM",
    "affected_component": "Analytics Dashboard",
    "impact": "Data visualization unavailable"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-004'),
NOW() - INTERVAL '10 hours'),

('Frontend Team Investigation', 'UPDATED',
'{
  "description": "Frontend team analyzing dashboard rendering issues",
  "analysis_details": {
    "team": "Frontend",
    "initial_assessment": "JavaScript library conflict",
    "affected_features": ["Charts", "Graphs", "Data tables"],
    "priority": "P2"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-004'),
NOW() - INTERVAL '9 hours 45 minutes'),

('Dependency Analysis', 'ANALYSIS',
'{
  "description": "Completed analysis of dashboard dependencies",
  "analysis_details": {
    "findings": {
      "root_cause": "Outdated chart library",
      "conflicts": ["CSS framework", "Data binding"],
      "recommended_fix": "Library version upgrade"
    }
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-004'),
NOW() - INTERVAL '9 hours 30 minutes'),

('Library Update Deployment', 'UPDATED',
'{
  "description": "Deploying updated dashboard libraries",
  "alert_details": {
    "action": "Version upgrade",
    "components": ["Chart library", "CSS framework"],
    "status": "Deploying"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-004'),
NOW() - INTERVAL '9 hours 15 minutes'),

('Dashboard Functionality Restored', 'RESOLVED',
'{
  "description": "Dashboard rendering fully restored",
  "analysis_details": {
    "resolution": "Library versions updated successfully",
    "verification": {
      "visual_tests": "Passed",
      "performance": "Improved",
      "browser_compatibility": "Confirmed"
    }
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-004'),
NOW() - INTERVAL '9 hours'),

-- Incident 5: Memory Leak (INC-005)
('Memory Leak Detection', 'CREATED',
'{
  "description": "Memory leak detected in user service",
  "alert_details": {
    "source": "Resource Monitor",
    "severity": "HIGH",
    "affected_service": "User Service",
    "metrics": {
      "memory_growth": "2MB/minute",
      "current_usage": "85%"
    }
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-005'),
NOW() - INTERVAL '12 hours'),

('Backend Team Response', 'UPDATED',
'{
  "description": "Backend team investigating memory leak",
  "analysis_details": {
    "team": "Backend",
    "tools_used": ["Memory Profiler", "Heap Analyzer"],
    "initial_findings": "Session object accumulation",
    "priority": "P1"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-005'),
NOW() - INTERVAL '11 hours 45 minutes'),

('Memory Analysis Results', 'ANALYSIS',
'{
  "description": "Completed analysis of memory leak",
  "analysis_details": {
    "root_cause": "Unclosed database connections",
    "impact_areas": ["User sessions", "Connection pool"],
    "memory_pattern": "Linear growth",
    "fix_required": "Connection handling revision"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-005'),
NOW() - INTERVAL '11 hours 30 minutes'),

('Fix Implementation', 'UPDATED',
'{
  "description": "Deploying memory leak fixes",
  "alert_details": {
    "action": "Code deployment",
    "changes": ["Connection cleanup", "Session management"],
    "status": "In Progress"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-005'),
NOW() - INTERVAL '11 hours 15 minutes'),

('Memory Leak Resolved', 'RESOLVED',
'{
  "description": "Memory leak successfully fixed",
  "analysis_details": {
    "resolution": "Connection handling corrected",
    "metrics": {
      "memory_usage": "Stable",
      "leak_status": "Resolved",
      "service_health": "Normal"
    }
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-005'),
NOW() - INTERVAL '11 hours'),

-- Incident 6: SSL Certificate Expiration (INC-006)
('SSL Certificate Alert', 'CREATED',
'{
  "description": "SSL certificate expiration warning for production domains",
  "alert_details": {
    "source": "Certificate Monitor",
    "severity": "CRITICAL",
    "domain": "*.company.com",
    "expiry": "24 hours",
    "impact": "Potential service disruption"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-006'),
NOW() - INTERVAL '15 hours'),

('Security Team Response', 'UPDATED',
'{
  "description": "Security team initiating certificate renewal process",
  "analysis_details": {
    "team": "Security",
    "priority": "P0",
    "initial_steps": ["Domain validation", "CSR generation"],
    "timeline": "Critical - must complete within 24 hours"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-006'),
NOW() - INTERVAL '14 hours 45 minutes'),

('Certificate Process Analysis', 'ANALYSIS',
'{
  "description": "Analysis of certificate management process completed",
  "analysis_details": {
    "current_state": "Manual renewal process",
    "risks": ["Human error", "Timing dependencies"],
    "recommendation": "Implement automated renewal",
    "long_term_action": "Certificate automation setup"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-006'),
NOW() - INTERVAL '14 hours 30 minutes'),

('Certificate Renewal', 'UPDATED',
'{
  "description": "New SSL certificate being deployed",
  "alert_details": {
    "action": "Certificate deployment",
    "provider": "Lets Encrypt",
    "validity": "12 months",
    "status": "In Progress"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-006'),
NOW() - INTERVAL '14 hours 15 minutes'),

('Certificate Update Complete', 'RESOLVED',
'{
  "description": "SSL certificate successfully renewed and deployed",
  "analysis_details": {
    "resolution": "New certificate installed",
    "verification": {
      "ssl_check": "Valid",
      "expiry": "12 months",
      "domains": "Verified"
    }
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-006'),
NOW() - INTERVAL '14 hours'),

-- Incident 7: Network Connectivity (INC-007)
('Network Connectivity Alert', 'CREATED',
'{
  "description": "Network connectivity issues between data centers",
  "alert_details": {
    "source": "Network Monitor",
    "severity": "HIGH",
    "affected_routes": ["DC1-DC2", "DC1-DC3"],
    "metrics": {
      "packet_loss": "25%",
      "latency": "High"
    }
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-007'),
NOW() - INTERVAL '18 hours'),

('Network Team Engagement', 'UPDATED',
'{
  "description": "Network team investigating connectivity issues",
  "analysis_details": {
    "team": "Network Operations",
    "tools": ["Route Tracers", "Packet Analyzers"],
    "initial_findings": "Route congestion detected",
    "priority": "P1"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-007'),
NOW() - INTERVAL '17 hours 45 minutes'),

('Network Analysis Complete', 'ANALYSIS',
'{
  "description": "Detailed analysis of network issues completed",
  "analysis_details": {
    "root_cause": "Primary route saturation",
    "affected_services": ["Data Replication", "API Traffic"],
    "impact": "Cross-DC communication degraded",
    "solution": "Traffic rerouting required"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-007'),
NOW() - INTERVAL '17 hours 30 minutes'),

('Traffic Rerouting', 'UPDATED',
'{
  "description": "Implementing network traffic rerouting",
  "alert_details": {
    "action": "Route reconfiguration",
    "changes": ["Backup path activation", "Load balancing update"],
    "status": "In Progress"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-007'),
NOW() - INTERVAL '17 hours 15 minutes'),

('Network Stability Restored', 'RESOLVED',
'{
  "description": "Network connectivity fully restored between data centers",
  "analysis_details": {
    "resolution": "Traffic successfully rerouted",
    "metrics": {
      "packet_loss": "0.1%",
      "latency": "Normal",
      "bandwidth": "Optimal"
    }
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-007'),
NOW() - INTERVAL '17 hours'),

-- Incident 8: Storage Capacity (INC-008)
('Storage Capacity Alert', 'CREATED',
'{
  "description": "Primary storage system approaching capacity limits",
  "alert_details": {
    "source": "Storage Monitor",
    "severity": "HIGH",
    "current_usage": "95%",
    "growth_rate": "2% per day",
    "estimated_time_to_full": "2.5 days"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-008'),
NOW() - INTERVAL '20 hours'),

('Storage Team Response', 'UPDATED',
'{
  "description": "Storage team beginning capacity management",
  "analysis_details": {
    "team": "Storage Operations",
    "initial_assessment": "Log file accumulation",
    "priority": "P2",
    "immediate_actions": ["Usage analysis", "Cleanup planning"]
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-008'),
NOW() - INTERVAL '19 hours 45 minutes'),

('Storage Analysis Results', 'ANALYSIS',
'{
  "description": "Completed analysis of storage usage patterns",
  "analysis_details": {
    "findings": {
      "large_files": "Log archives",
      "old_backups": "Redundant copies",
      "potential_savings": "30%"
    },
    "recommendation": "Implement cleanup and archival"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-008'),
NOW() - INTERVAL '19 hours 30 minutes'),

('Storage Optimization', 'UPDATED',
'{
  "description": "Implementing storage optimization measures",
  "alert_details": {
    "action": "Storage cleanup",
    "tasks": ["Log rotation", "Backup consolidation"],
    "status": "In Progress"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-008'),
NOW() - INTERVAL '19 hours 15 minutes'),

('Storage Issue Resolved', 'RESOLVED',
'{
  "description": "Storage capacity issues successfully resolved",
  "analysis_details": {
    "resolution": "Storage optimization completed",
    "metrics": {
      "current_usage": "65%",
      "space_freed": "30%",
      "growth_rate": "Stabilized"
    }
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-008'),
NOW() - INTERVAL '19 hours'),

-- Incident 9: Query Performance (INC-009)
('Query Timeout Alert', 'CREATED',
'{
  "description": "Database query timeouts affecting user service",
  "alert_details": {
    "source": "Database Monitor",
    "severity": "MEDIUM",
    "affected_queries": "User profile lookups",
    "timeout_count": "25/minute",
    "impact": "User experience degradation"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-009'),
NOW() - INTERVAL '22 hours'),

('DBA Team Investigation', 'UPDATED',
'{
  "description": "Database team investigating query performance",
  "analysis_details": {
    "team": "Database Administration",
    "tools_used": ["Query Analyzer", "Explain Plan"],
    "initial_findings": "Missing indexes identified",
    "priority": "P2"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-009'),
NOW() - INTERVAL '21 hours 45 minutes'),

('Query Analysis Complete', 'ANALYSIS',
'{
  "description": "Detailed analysis of query performance completed",
  "analysis_details": {
    "findings": {
      "problem_queries": "Identified",
      "missing_indexes": "Confirmed",
      "table_statistics": "Outdated"
    },
    "optimization_plan": "Index creation and statistics update"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-009'),
NOW() - INTERVAL '21 hours 30 minutes'),

('Performance Optimization', 'UPDATED',
'{
  "description": "Implementing database performance improvements",
  "alert_details": {
    "action": "Database optimization",
    "changes": ["Index creation", "Statistics update"],
    "status": "In Progress"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-009'),
NOW() - INTERVAL '21 hours 15 minutes'),

('Query Performance Restored', 'RESOLVED',
'{
  "description": "Query performance issues successfully resolved",
  "analysis_details": {
    "resolution": "Optimization completed",
    "metrics": {
      "query_time": "Improved by 85%",
      "timeout_count": "0",
      "user_experience": "Normalized"
    }
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-009'),
NOW() - INTERVAL '21 hours'),

-- Incident 10: API Rate Limiting (INC-010)
('Rate Limit Alert', 'CREATED',
'{
  "description": "API rate limiting causing service disruptions",
  "alert_details": {
    "source": "API Gateway",
    "severity": "HIGH",
    "affected_endpoints": ["User API", "Order API"],
    "error_rate": "15%",
    "client_impact": "Service degradation"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-010'),
NOW() - INTERVAL '25 hours'),

('API Team Response', 'UPDATED',
'{
  "description": "API team investigating rate limiting issues",
  "analysis_details": {
    "team": "API Platform",
    "initial_assessment": "Throttling thresholds too low",
    "affected_clients": 15,
    "priority": "P1"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-010'),
NOW() - INTERVAL '24 hours 45 minutes'),

('Traffic Analysis Complete', 'ANALYSIS',
'{
  "description": "Analysis of API traffic patterns completed",
  "analysis_details": {
    "findings": {
      "peak_traffic": "150 req/sec",
      "throttle_limit": "100 req/sec",
      "client_patterns": "Normal business usage"
    },
    "recommendation": "Increase rate limits"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-010'),
NOW() - INTERVAL '24 hours 30 minutes'),

('Rate Limit Adjustment', 'UPDATED',
'{
  "description": "Implementing new rate limit configuration",
  "alert_details": {
    "action": "Throttling update",
    "changes": ["Increased limits", "Client notification"],
    "status": "In Progress"
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-010'),
NOW() - INTERVAL '24 hours 15 minutes'),

('API Service Stabilized', 'RESOLVED',
'{
  "description": "API rate limiting issues successfully resolved",
  "analysis_details": {
    "resolution": "Rate limits optimized",
    "metrics": {
      "error_rate": "0%",
      "client_satisfaction": "Restored",
      "api_performance": "Optimal"
    }
  }
}',
(SELECT id FROM users WHERE email = '<EMAIL>'),
(SELECT id FROM incidents WHERE incident_number = 'INC-010'),
NOW() - INTERVAL '24 hours');

-- Incident Metrics Table
CREATE TABLE IF NOT EXISTS incident_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    incident_id UUID NOT NULL REFERENCES incidents(id) ON DELETE CASCADE,
    detected_time TIMESTAMPTZ,
    reported_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    acknowledged_time TIMESTAMPTZ,
    resolved_time TIMESTAMPTZ,
    closed_time TIMESTAMPTZ,
    time_to_report INTERVAL GENERATED ALWAYS AS (reported_time - COALESCE(detected_time, reported_time)) STORED,
    time_to_acknowledge INTERVAL GENERATED ALWAYS AS (acknowledged_time - reported_time) STORED,
    time_to_resolve INTERVAL GENERATED ALWAYS AS (resolved_time - acknowledged_time) STORED,
    time_to_closure INTERVAL GENERATED ALWAYS AS (closed_time - resolved_time) STORED,
    total_downtime INTERVAL GENERATED ALWAYS AS (resolved_time - COALESCE(detected_time, reported_time)) STORED
);

-- Insert metrics for each incident
INSERT INTO incident_metrics (incident_id, detected_time, reported_time, acknowledged_time, resolved_time, closed_time)
VALUES
-- INC-001: API Gateway Outage
((SELECT id FROM incidents WHERE incident_number = 'INC-001'),
 NOW() - INTERVAL '2 hours 5 minutes',
 NOW() - INTERVAL '2 hours',
 NOW() - INTERVAL '1 hour 45 minutes',
 NOW() - INTERVAL '1 hour 30 minutes',
 NULL),

-- INC-002: Database Connection Failure
((SELECT id FROM incidents WHERE incident_number = 'INC-002'),
 NOW() - INTERVAL '47 minutes',
 NOW() - INTERVAL '45 minutes',
 NOW() - INTERVAL '30 minutes',
 NULL,
 NULL),

-- INC-003: Authentication Service Degradation
((SELECT id FROM incidents WHERE incident_number = 'INC-003'),
 NOW() - INTERVAL '3 hours 10 minutes',
 NOW() - INTERVAL '3 hours',
 NOW() - INTERVAL '2 hours 45 minutes',
 NOW() - INTERVAL '2 hours',
 NOW() - INTERVAL '1 hour'),

-- INC-004: UI Dashboard Issue
((SELECT id FROM incidents WHERE incident_number = 'INC-004'),
 NOW() - INTERVAL '1 day 15 minutes',
 NOW() - INTERVAL '1 day',
 NOW() - INTERVAL '23 hours',
 NOW() - INTERVAL '22 hours',
 NOW() - INTERVAL '21 hours'),

-- INC-005: Memory Leak
((SELECT id FROM incidents WHERE incident_number = 'INC-005'),
 NOW() - INTERVAL '2 days 20 minutes',
 NOW() - INTERVAL '2 days',
 NOW() - INTERVAL '1 day 23 hours',
 NOW() - INTERVAL '1 day 20 hours',
 NOW() - INTERVAL '1 day 18 hours'),

-- INC-006: SSL Certificate Expiration
((SELECT id FROM incidents WHERE incident_number = 'INC-006'),
 NOW() - INTERVAL '1 minute',
 NOW(),
 NULL,
 NULL,
 NULL),

-- INC-007: Network Connectivity Issues
((SELECT id FROM incidents WHERE incident_number = 'INC-007'),
 NOW() - INTERVAL '5 hours 3 minutes',
 NOW() - INTERVAL '5 hours',
 NOW() - INTERVAL '4 hours 45 minutes',
 NULL,
 NULL),

-- INC-008: Storage Capacity Warning
((SELECT id FROM incidents WHERE incident_number = 'INC-008'),
 NOW() - INTERVAL '20 hours 10 minutes',
 NOW() - INTERVAL '20 hours',
 NOW() - INTERVAL '19 hours 30 minutes',
 NOW() - INTERVAL '19 hours',
 NULL),

-- INC-009: Database Query Timeout
((SELECT id FROM incidents WHERE incident_number = 'INC-009'),
 NOW() - INTERVAL '4 days 12 minutes',
 NOW() - INTERVAL '4 days',
 NOW() - INTERVAL '3 days 23 hours',
 NOW() - INTERVAL '3 days 21 hours',
 NOW() - INTERVAL '3 days 20 hours'),

-- INC-010: API Rate Limiting
((SELECT id FROM incidents WHERE incident_number = 'INC-010'),
 NOW() - INTERVAL '1 day 7 minutes',
 NOW() - INTERVAL '1 day',
 NOW() - INTERVAL '23 hours',
 NULL,
 NULL);

CREATE TABLE IF NOT EXISTS incident_report (
    executive_summary TEXT PRIMARY KEY,
    incident_id UUID NOT NULL REFERENCES incidents(id) ON DELETE CASCADE,
    post_incident_actions JSON,
    retrospectives JSON,
    action_items JSON
);

-- Sample data for incident_report for each incident
INSERT INTO incident_report (
    executive_summary, incident_id, post_incident_actions, retrospectives, action_items
) VALUES
    ('API Gateway outage resolved after load balancer fix.', (SELECT id FROM incidents WHERE incident_number = 'INC-001'),
     '["Rollback deployment", "Notify stakeholders"]',
     '{"what_went_well": ["Rapid detection", "Effective team collaboration", "Clear escalation process", "Comprehensive monitoring in place"], "areas_for_improvement": ["Improve deployment validation", "Enhance rollback documentation", "Increase incident drill frequency", "Automate alert testing"]}',
     '[{"action": "Add deployment validation to CI/CD", "status": "In Progress"}, {"action": "Review monitoring rules", "status": "Completed"}]'),
    ('Database connection failure mitigated by pool optimization.', (SELECT id FROM incidents WHERE incident_number = 'INC-002'),
     '["Increase pool size", "Optimize connection handling"]',
     '{"what_went_well": ["Quick DBA response", "Good monitoring alerts", "Effective use of failover", "Timely communication with stakeholders"], "areas_for_improvement": ["Better connection leak detection", "Improve failover process", "Enhance DB documentation", "Automate failover testing"]}',
     '[{"action": "Implement leak detection", "status": "Planned"}, {"action": "Document failover steps", "status": "Completed"}]'),
    ('Authentication service latency resolved by cache optimization.', (SELECT id FROM incidents WHERE incident_number = 'INC-003'),
     '["Increase cache size", "Tune eviction policy"]',
     '{"what_went_well": ["Fast root cause analysis", "Effective cache tuning", "Good cross-team support", "Proactive alerting"], "areas_for_improvement": ["Improve cache monitoring", "Automate cache scaling", "Increase cache hit ratio", "Document cache configuration"]}',
     '[{"action": "Set up cache alerts", "status": "In Progress"}, {"action": "Automate scaling", "status": "Not Started"}]'),
    ('Dashboard rendering issue fixed by library update.', (SELECT id FROM incidents WHERE incident_number = 'INC-004'),
     '["Upgrade chart library", "Test UI components"]',
     '{"what_went_well": ["Quick frontend fix", "Good user feedback", "Effective QA process", "Responsive design improvements"], "areas_for_improvement": ["Test library updates", "Improve UI regression tests", "Enhance browser compatibility", "Automate visual testing"]}',
     '[{"action": "Automate UI tests", "status": "Planned"}, {"action": "Review update process", "status": "Completed"}]'),
    ('Memory leak in user service resolved by code fix.', (SELECT id FROM incidents WHERE incident_number = 'INC-005'),
     '["Fix connection handling", "Deploy memory profiler"]',
     '{"what_went_well": ["Effective profiling", "Quick deployment", "Good use of monitoring tools", "Strong collaboration with QA"], "areas_for_improvement": ["Monitor memory usage", "Improve code review", "Automate memory profiling", "Enhance developer training"]}',
     '[{"action": "Add memory alerts", "status": "In Progress"}, {"action": "Enhance code review", "status": "Completed"}]'),
    ('SSL certificate renewed before expiration.', (SELECT id FROM incidents WHERE incident_number = 'INC-006'),
     '["Automate renewal", "Notify stakeholders"]',
     '{"what_went_well": ["Proactive monitoring", "Quick renewal", "Effective documentation", "Good vendor communication"], "areas_for_improvement": ["Automate notifications", "Improve renewal documentation", "Test renewal process", "Increase renewal reminders"]}',
     '[{"action": "Set up auto-renewal", "status": "Planned"}, {"action": "Document process", "status": "Completed"}]'),
    ('Network connectivity restored by rerouting traffic.', (SELECT id FROM incidents WHERE incident_number = 'INC-007'),
     '["Activate backup routes", "Update network docs"]',
     '{"what_went_well": ["Fast reroute", "Good communication", "Effective use of monitoring tools", "Strong vendor support"], "areas_for_improvement": ["Test backup routes", "Improve monitoring", "Enhance network diagrams", "Automate failover"]}',
     '[{"action": "Test backup routes", "status": "In Progress"}, {"action": "Enhance monitoring", "status": "Completed"}]'),
    ('Storage capacity issue resolved by cleanup and expansion.', (SELECT id FROM incidents WHERE incident_number = 'INC-008'),
     '["Clean up old logs", "Expand storage"]',
     '{"what_went_well": ["Quick cleanup", "Effective expansion", "Good forecasting", "Efficient resource allocation"], "areas_for_improvement": ["Monitor disk usage", "Automate cleanup", "Improve storage alerts", "Document storage policies"]}',
     '[{"action": "Set up disk alerts", "status": "Planned"}, {"action": "Automate cleanup", "status": "Not Started"}]'),
    ('Query timeouts resolved by index optimization.', (SELECT id FROM incidents WHERE incident_number = 'INC-009'),
     '["Add missing indexes", "Update statistics"]',
     '{"what_went_well": ["Effective indexing", "Good DBA collaboration", "Timely performance review", "Comprehensive query analysis"], "areas_for_improvement": ["Monitor query performance", "Automate index review", "Improve query documentation", "Increase query test coverage"]}',
     '[{"action": "Monitor query times", "status": "In Progress"}, {"action": "Schedule index review", "status": "Completed"}]'),
    ('API rate limiting adjusted to prevent 503 errors.', (SELECT id FROM incidents WHERE incident_number = 'INC-010'),
     '["Increase rate limits", "Notify clients"]',
     '{"what_went_well": ["Quick config change", "Good client communication", "Effective alerting", "Proactive client support"], "areas_for_improvement": ["Monitor rate limits", "Improve client notification", "Automate rate limit adjustments", "Enhance client documentation"]}',
     '[{"action": "Set up rate limit alerts", "status": "Planned"}, {"action": "Document notification process", "status": "Completed"}]');
