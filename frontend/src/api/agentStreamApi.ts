import { getAccessToken } from '../utils/authHelper';
import { config } from '../utils/configService';
import { getCurrentSessionId } from '../utils/sessionManager';

export interface FunctionCall {
  name: string;
  args: Record<string, any>;
}

export interface FunctionResponse {
  name: string;
  response: Record<string, any>;
}

export interface EventActions {
  state_delta?: Record<string, any>;
  artifact_delta?: Record<string, any>;
  transfer_to_agent?: string;
  escalate?: boolean;
  skip_summarization?: boolean;
}

export interface StreamEvent {
  author: string;
  type: string;
  event_id?: string;
  invocation_id?: string;
  is_final: boolean;
  is_partial: boolean;
  turn_complete?: boolean;
  content?: string;
  content_type?: string;
  error_code?: string;
  error_message?: string;
  timestamp?: string;
  actions: EventActions;
  function_calls: FunctionCall[];
  function_responses: FunctionResponse[];
}

export interface StreamingOptions {
  onEvent?: (event: StreamEvent) => void;
  onError?: (error: Error) => void;
  onComplete?: () => void;
  signal?: AbortSignal;
}

/**
 * Stream  agent responses using Server-Sent Events
 */
export async function streamAgentQuery(
  query: string,
  options: StreamingOptions = {},
): Promise<void> {
  const { onEvent, onError, onComplete, signal } = options;

  const token = getAccessToken();
  if (!token) {
    const error = new Error('No authentication token available');
    onError?.(error);
    throw error;
  }

  const baseUrl = config.getApiUrl();
  const sessionId = getCurrentSessionId();

  try {
    // For EventSource, we need to handle auth differently since it doesn't support custom headers
    // We'll use the POST endpoint with fetch streaming instead
    const response = await fetch(`${baseUrl}/agents/query_stream`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query, session_id: sessionId }),
      signal,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    if (!response.body) {
      throw new Error('No response body');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const eventData: StreamEvent = JSON.parse(line.slice(6));

              if (eventData.type === 'stream_complete') {
                onComplete?.();
                return;
              }

              if (eventData.type === 'error') {
                const error = new Error(
                  eventData.error_message || 'Stream error',
                );
                onError?.(error);
                return;
              }

              onEvent?.(eventData);

              if (eventData.is_final) {
                onComplete?.();
                return;
              }
            } catch (parseError) {
              console.error('Error parsing event data:', parseError);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  } catch (error) {
    if (error instanceof Error) {
      onError?.(error);
      throw error;
    } else {
      const unknownError = new Error('Unknown streaming error');
      onError?.(unknownError);
      throw unknownError;
    }
  }
}
