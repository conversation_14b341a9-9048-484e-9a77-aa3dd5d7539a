import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Container,
  Group,
  LoadingOverlay,
  Paper,
  ScrollArea,
  Stack,
  Text,
  Textarea,
  Title,
  Collapse,
  ActionIcon,
  Divider,
  Code,
} from '@mantine/core';
import { useForm } from 'react-hook-form';

import {
  Bot,
  Send,
  AlertCircle,
  CheckCircle,
  Clock,
  RotateCcw,
  Settings,
  ArrowRight,
  Database,
  FileText,
  ChevronDown,
  ChevronRight,
} from 'lucide-react';
import { useState, useRef, useEffect } from 'react';
import {
  streamAgentQuery,
  StreamEvent,
  FunctionCall,
  FunctionResponse,
  EventActions,
} from '../api/agentStreamApi';
import { getCurrentSessionId, startNewSession } from '../utils/sessionManager';

interface FormData {
  query: string;
}

interface Message {
  id: string;
  type: 'user' | 'agent' | 'event';
  content: string;
  timestamp: Date;
  author?: string;
  eventType?: string;
  contentType?: string;
  isError?: boolean;
  isFinal?: boolean;
  isPartial?: boolean;
  eventId?: string;
  invocationId?: string;
  functionCalls?: FunctionCall[];
  functionResponses?: FunctionResponse[];
  actions?: EventActions;
  showDetails?: boolean;
}

const initialMessage = (sessionId: string): Message => {
  return {
    id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
    timestamp: new Date(),
    type: 'event',
    content: `Welcome to Abilytics AI Agent!\nSession ID: ${sessionId}\n\nDescribe your incident or system issue, and I'll guide you through a comprehensive resolution process.`,
    isFinal: true,
  };
};

export default function AgentSpace() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [eventSource, setEventSource] = useState<EventSource | null>(null);
  const [currentSessionId, setCurrentSessionId] = useState<string>(
    getCurrentSessionId(),
  );
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const form = useForm<FormData>({
    defaultValues: {
      query: '',
    },
  });

  // Initialize session ID on component mount
  useEffect(() => {
    setMessages([]);
    // Add welcome message with session info
    addMessage(initialMessage(currentSessionId));
  }, [currentSessionId]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const addMessage = (message: Omit<Message, 'id' | 'timestamp'>) => {
    const newMessage: Message = {
      ...message,
      id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const toggleMessageDetails = (messageId: string) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId ? { ...msg, showDetails: !msg.showDetails } : msg,
      ),
    );
  };

  const stopStreaming = () => {
    if (eventSource) {
      eventSource.close();
      setEventSource(null);
    }
    setIsStreaming(false);
  };

  const handleNewSession = () => {
    if (isStreaming) {
      stopStreaming();
    }
    const newSessionId = startNewSession();
    setCurrentSessionId(newSessionId);
  };

  const handleSubmit = async (data: FormData) => {
    if (isStreaming) {
      stopStreaming();
      return;
    }

    const query = data.query.trim();
    if (!query) return;

    // Add user message
    addMessage({
      type: 'user',
      content: query,
    });

    // Clear the form
    form.reset({ query: '' });

    setIsStreaming(true);

    try {
      await streamAgentQuery(query, {
        onEvent: (event: StreamEvent) => {
          if (event.content) {
            addMessage({
              type: 'agent',
              content: event.content,
              author: event.author,
              eventType: event.type,
              contentType: event.content_type,
              isFinal: event.is_final,
              isPartial: event.is_partial,
              eventId: event.event_id,
              invocationId: event.invocation_id,
              functionCalls: event.function_calls,
              functionResponses: event.function_responses,
              actions: event.actions,
              showDetails: false,
            });
          }
        },
        onError: (error: Error) => {
          console.error('Streaming error:', error);
          addMessage({
            type: 'event',
            content: `Error: ${error.message}`,
            isError: true,
            isFinal: true,
          });
          console.error(
            'Connection Error: Failed to connect to the AI Agent. Please try later.',
          );
          setIsStreaming(false);
        },
        onComplete: () => {
          setIsStreaming(false);
        },
      });
    } catch (error) {
      console.error('Streaming setup error:', error);
      addMessage({
        type: 'event',
        content: `Setup Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        isError: true,
        isFinal: true,
      });
      setIsStreaming(false);
    }
  };

  const getMessageIcon = (message: Message) => {
    if (message.type === 'user') return null;

    // Icon based on content type
    switch (message.contentType) {
      case 'tool_call_request':
        return <Settings size={16} className="text-blue-500" />;
      case 'tool_result':
        return <CheckCircle size={16} className="text-green-500" />;
      case 'streaming_text':
        return <Clock size={16} className="text-yellow-500 animate-pulse" />;
      case 'agent_transfer':
        return <ArrowRight size={16} className="text-purple-500" />;
      case 'escalation':
        return <AlertCircle size={16} className="text-red-500" />;
      case 'state_update':
        return <Database size={16} className="text-indigo-500" />;
      case 'error':
        return <AlertCircle size={16} className="text-red-500" />;
      case 'long_running_tool':
        return <Clock size={16} className="text-orange-500" />;
      default:
        if (message.isError)
          return <AlertCircle size={16} className="text-red-500" />;
        if (message.isFinal)
          return <CheckCircle size={16} className="text-green-500" />;
        return <Clock size={16} className="text-blue-500" />;
    }
  };

  const getMessageBadgeColor = (message: Message) => {
    switch (message.contentType) {
      case 'tool_call_request':
        return 'blue';
      case 'tool_result':
        return 'green';
      case 'streaming_text':
        return 'yellow';
      case 'agent_transfer':
        return 'violet';
      case 'escalation':
        return 'red';
      case 'state_update':
        return 'indigo';
      case 'error':
        return 'red';
      case 'long_running_tool':
        return 'orange';
      default:
        if (message.isError) return 'red';
        if (message.isFinal) return 'green';
        return 'blue';
    }
  };

  const getMessageBackgroundClass = (message: Message) => {
    if (message.type === 'user') {
      return 'bg-blue-50 border-blue-200 ml-8';
    }

    // Enhanced styling based on content type
    switch (message.contentType) {
      case 'tool_call_request':
        return 'bg-blue-50 border-blue-300 mr-8';
      case 'tool_result':
        return 'bg-green-50 border-green-300 mr-8';
      case 'streaming_text':
        return 'bg-yellow-50 border-yellow-200 mr-8';
      case 'agent_transfer':
        return 'bg-violet-50 border-violet-300 mr-8';
      case 'escalation':
        return 'bg-red-50 border-red-300 mr-8';
      case 'state_update':
        return 'bg-indigo-50 border-indigo-300 mr-8';
      case 'error':
        return 'bg-red-50 border-red-400 mr-8';
      case 'long_running_tool':
        return 'bg-orange-50 border-orange-300 mr-8';
      default:
        return 'bg-gray-50 border-gray-200 mr-8';
    }
  };

  const renderEventDetails = (message: Message) => {
    if (!message.showDetails) return null;

    const hasDetails =
      message.functionCalls?.length ||
      message.functionResponses?.length ||
      message.actions?.state_delta ||
      message.actions?.artifact_delta ||
      message.eventId ||
      message.invocationId;

    if (!hasDetails) return null;

    return (
      <Collapse in={message.showDetails}>
        <Divider my="xs" />
        <Stack gap="xs">
          {/* Event IDs */}
          {(message.eventId || message.invocationId) && (
            <Group gap="xs">
              <Text size="xs" c="dimmed" fw={500}>
                IDs:
              </Text>
              {message.eventId && <Code fz="xs">Event: {message.eventId}</Code>}
              {message.invocationId && (
                <Code fz="xs">Invocation: {message.invocationId}</Code>
              )}
            </Group>
          )}

          {/* Function Calls */}
          {message.functionCalls?.length && (
            <div>
              <Text size="xs" c="dimmed" fw={500} mb="xs">
                Tool Calls:
              </Text>
              {message.functionCalls.map((call, idx) => (
                <Paper key={idx} p="xs" radius="sm" className="bg-blue-50">
                  <Group gap="xs">
                    <Settings size={14} className="text-blue-600" />
                    <Text size="xs" fw={500}>
                      {call.name}
                    </Text>
                  </Group>
                  {Object.keys(call.args).length > 0 && (
                    <Code block fz="xs" mt="xs">
                      {JSON.stringify(call.args, null, 2)}
                    </Code>
                  )}
                </Paper>
              ))}
            </div>
          )}

          {/* Function Responses */}
          {message.functionResponses?.length && (
            <div>
              <Text size="xs" c="dimmed" fw={500} mb="xs">
                Tool Results:
              </Text>
              {message.functionResponses.map((response, idx) => (
                <Paper key={idx} p="xs" radius="sm" className="bg-green-50">
                  <Group gap="xs">
                    <CheckCircle size={14} className="text-green-600" />
                    <Text size="xs" fw={500}>
                      {response.name}
                    </Text>
                  </Group>
                  <Code block fz="xs" mt="xs">
                    {JSON.stringify(response.response, null, 2)}
                  </Code>
                </Paper>
              ))}
            </div>
          )}

          {/* State Changes */}
          {message.actions?.state_delta &&
            Object.keys(message.actions.state_delta).length > 0 && (
              <div>
                <Text size="xs" c="dimmed" fw={500} mb="xs">
                  State Changes:
                </Text>
                <Paper p="xs" radius="sm" className="bg-indigo-50">
                  <Group gap="xs" mb="xs">
                    <Database size={14} className="text-indigo-600" />
                    <Text size="xs" fw={500}>
                      State Delta
                    </Text>
                  </Group>
                  <Code block fz="xs">
                    {JSON.stringify(message.actions.state_delta, null, 2)}
                  </Code>
                </Paper>
              </div>
            )}

          {/* Artifact Changes */}
          {message.actions?.artifact_delta &&
            Object.keys(message.actions.artifact_delta).length > 0 && (
              <div>
                <Text size="xs" c="dimmed" fw={500} mb="xs">
                  Artifact Changes:
                </Text>
                <Paper p="xs" radius="sm" className="bg-purple-50">
                  <Group gap="xs" mb="xs">
                    <FileText size={14} className="text-purple-600" />
                    <Text size="xs" fw={500}>
                      Artifact Delta
                    </Text>
                  </Group>
                  <Code block fz="xs">
                    {JSON.stringify(message.actions.artifact_delta, null, 2)}
                  </Code>
                </Paper>
              </div>
            )}

          {/* Control Flow */}
          {(message.actions?.transfer_to_agent ||
            message.actions?.escalate) && (
            <div>
              <Text size="xs" c="dimmed" fw={500} mb="xs">
                Control Flow:
              </Text>
              {message.actions.transfer_to_agent && (
                <Paper p="xs" radius="sm" className="bg-violet-50">
                  <Group gap="xs">
                    <ArrowRight size={14} className="text-violet-600" />
                    <Text size="xs" fw={500}>
                      Transfer to: {message.actions.transfer_to_agent}
                    </Text>
                  </Group>
                </Paper>
              )}
              {message.actions.escalate && (
                <Paper p="xs" radius="sm" className="bg-red-50">
                  <Group gap="xs">
                    <AlertCircle size={14} className="text-red-600" />
                    <Text size="xs" fw={500}>
                      Escalated
                    </Text>
                  </Group>
                </Paper>
              )}
            </div>
          )}
        </Stack>
      </Collapse>
    );
  };

  return (
    <Container fluid p="md" className="h-full">
      <Stack gap="md" className="h-full">
        {/* Header */}
        <Paper p="lg" radius="md" withBorder>
          <Group justify="space-between">
            <Group gap="md">
              <Bot size={32} className="text-blue-600" />
              <div>
                <Title order={2}>Abilytics AI Agent</Title>
                <Group gap="md" mt="xs">
                  <Text size="sm" c="dimmed">
                    Your AI Teammate
                  </Text>
                </Group>
              </div>
            </Group>
            <Button
              variant="light"
              leftSection={<RotateCcw size={16} />}
              onClick={handleNewSession}
              disabled={isStreaming}
            >
              New Session
            </Button>
          </Group>
        </Paper>

        {/* Messages Area */}
        <Card className="flex-1 flex flex-col" p="md">
          <ScrollArea className="flex-1 mb-4" scrollbarSize={8}>
            <Stack gap="md">
              {messages.map(message => (
                <Paper
                  key={message.id}
                  p="md"
                  radius="md"
                  className={`${getMessageBackgroundClass(message)} border`}
                >
                  <Group gap="xs" mb="xs" justify="space-between">
                    <Group gap="xs">
                      {getMessageIcon(message)}
                      <Text size="sm" fw={500}>
                        {message.type === 'user'
                          ? 'You'
                          : message.author || 'Agent'}
                      </Text>
                      {message.contentType && (
                        <Badge size="xs" color={getMessageBadgeColor(message)}>
                          {message.contentType.replace('_', ' ')}
                        </Badge>
                      )}
                      {message.eventType &&
                        message.contentType !== message.eventType && (
                          <Badge size="xs" variant="light" color="gray">
                            {message.eventType}
                          </Badge>
                        )}
                      <Text size="xs" c="dimmed">
                        {message.timestamp.toLocaleTimeString()}
                      </Text>
                    </Group>

                    {/* Details toggle for agent messages with enhanced data */}
                    {message.type === 'agent' &&
                      (message.functionCalls?.length ||
                        message.functionResponses?.length ||
                        message.actions?.state_delta ||
                        message.actions?.artifact_delta ||
                        message.eventId ||
                        message.invocationId) && (
                        <ActionIcon
                          size="xs"
                          variant="subtle"
                          color="gray"
                          onClick={() => toggleMessageDetails(message.id)}
                        >
                          {message.showDetails ? (
                            <ChevronDown size={12} />
                          ) : (
                            <ChevronRight size={12} />
                          )}
                        </ActionIcon>
                      )}
                  </Group>
                  <Text size="sm" style={{ whiteSpace: 'pre-wrap' }}>
                    {message.content}
                  </Text>

                  {/* Enhanced event details */}
                  {renderEventDetails(message)}
                </Paper>
              ))}

              {isStreaming && (
                <Paper
                  p="md"
                  radius="md"
                  className="bg-yellow-50 border-yellow-200 border mr-8"
                >
                  <Group gap="xs">
                    <LoadingOverlay visible={false} />
                    <Clock
                      size={16}
                      className="text-yellow-600 animate-pulse"
                    />
                    <Text size="sm" fw={500}>
                      Processing...
                    </Text>
                  </Group>
                  <Text size="sm" c="dimmed">
                    Abilytics AI Agent is analyzing your request...
                  </Text>
                </Paper>
              )}
            </Stack>
            <div ref={messagesEndRef} />
          </ScrollArea>

          {/* Input Form */}
          <form onSubmit={form.handleSubmit(handleSubmit)}>
            <Group gap="md" align="flex-end">
              <Textarea
                {...form.register('query', {
                  required: 'Please enter a query',
                  validate: value =>
                    value.trim().length > 0 || 'Please enter a query',
                })}
                placeholder="Describe your incident or system issue..."
                disabled={isStreaming}
                autosize
                minRows={2}
                maxRows={4}
                className="flex-1"
                error={form.formState.errors.query?.message}
              />
              <Button
                type="submit"
                loading={isStreaming}
                leftSection={isStreaming ? undefined : <Send size={16} />}
                color={isStreaming ? 'red' : 'blue'}
              >
                {isStreaming ? 'Stop' : 'Send'}
              </Button>
            </Group>
          </form>
        </Card>
      </Stack>
    </Container>
  );
}
