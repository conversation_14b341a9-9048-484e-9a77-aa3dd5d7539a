import React, { ReactNode } from 'react';
import { Navbar } from './Navbar';
import { Sidebar } from './Sidebar';
import { ScrollArea } from '@mantine/core';

interface LayoutProps {
  children: ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <>
      <Sidebar />
      <div className="h-full w-full flex flex-col p-0 relative">
        <Navbar />
        <ScrollArea>
          <main
            className="w-full h-full pt-[90px] px-4 sm:px-6 md:px-8 lg:px-10 pb-4 sm:pb-6 md:pb-8 lg:pb-10"
            role="main"
            aria-label="Main content"
            style={{
              minHeight: 'calc(100vh - 90px)',
              maxWidth: '100vw',
              overflow: 'hidden',
            }}
          >
            {children}
          </main>
        </ScrollArea>
      </div>
    </>
  );
};
