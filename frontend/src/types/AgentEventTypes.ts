/**
 * Type definitions for Google ADK agent events and SSE streaming
 */

export interface FunctionCall {
  name: string;
  args: Record<string, any>;
}

export interface FunctionResponse {
  name: string;
  response: Record<string, any>;
}

export interface EventActions {
  state_delta?: Record<string, any>;
  artifact_delta?: Record<string, any>;
  transfer_to_agent?: string;
  escalate?: boolean;
  skip_summarization?: boolean;
}

export interface StreamEvent {
  author: string;
  type: string;
  event_id?: string;
  invocation_id?: string;
  is_final: boolean;
  is_partial: boolean;
  turn_complete?: boolean;
  content?: string;
  content_type?: string;
  error_code?: string;
  error_message?: string;
  timestamp?: string;
  actions: EventActions;
  function_calls: FunctionCall[];
  function_responses: FunctionResponse[];
}

export interface Message {
  id: string;
  type: 'user' | 'agent' | 'event';
  content: string;
  timestamp: Date;
  author?: string;
  eventType?: string;
  contentType?: string;
  isError?: boolean;
  isFinal?: boolean;
  isPartial?: boolean;
  eventId?: string;
  invocationId?: string;
  functionCalls?: FunctionCall[];
  functionResponses?: FunctionResponse[];
  actions?: EventActions;
  showDetails?: boolean;
}

export interface EventSummary {
  total: number;
  byType: Record<string, number>;
  toolCalls: number;
  toolResults: number;
  stateChanges: number;
  artifactChanges: number;
}

/**
 * Available content types for agent events
 */
export type ContentType =
  | 'user_input'
  | 'streaming_text'
  | 'complete_text'
  | 'tool_call_request'
  | 'tool_result'
  | 'agent_transfer'
  | 'escalation'
  | 'state_update'
  | 'error'
  | 'long_running_tool'
  | 'unknown';

/**
 * Color mappings for different content types
 */
export const CONTENT_TYPE_COLORS: Record<ContentType, string> = {
  user_input: 'blue',
  streaming_text: 'yellow',
  complete_text: 'green',
  tool_call_request: 'blue',
  tool_result: 'green',
  agent_transfer: 'violet',
  escalation: 'red',
  state_update: 'indigo',
  error: 'red',
  long_running_tool: 'orange',
  unknown: 'gray',
};

/**
 * Background class mappings for different content types
 */
export const CONTENT_TYPE_BACKGROUNDS: Record<ContentType, string> = {
  user_input: 'bg-blue-50 border-blue-200 ml-8',
  streaming_text: 'bg-yellow-50 border-yellow-200 mr-8',
  complete_text: 'bg-gray-50 border-gray-200 mr-8',
  tool_call_request: 'bg-blue-50 border-blue-300 mr-8',
  tool_result: 'bg-green-50 border-green-300 mr-8',
  agent_transfer: 'bg-violet-50 border-violet-300 mr-8',
  escalation: 'bg-red-50 border-red-300 mr-8',
  state_update: 'bg-indigo-50 border-indigo-300 mr-8',
  error: 'bg-red-50 border-red-400 mr-8',
  long_running_tool: 'bg-orange-50 border-orange-300 mr-8',
  unknown: 'bg-gray-50 border-gray-200 mr-8',
};
